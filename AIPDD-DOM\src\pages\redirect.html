<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>爱嘉客服 - 正在跳转</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            color: #333;
        }
        .container {
            text-align: center;
            padding: 30px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
        }
        .logo {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 16px;
            font-weight: 500;
        }
        p {
            font-size: 16px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .loading {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        .dot {
            width: 12px;
            height: 12px;
            background: #1890ff;
            border-radius: 50%;
            margin: 0 6px;
            animation: bounce 1.5s infinite ease-in-out;
        }
        .dot:nth-child(2) {
            animation-delay: 0.2s;
        }
        .dot:nth-child(3) {
            animation-delay: 0.4s;
        }
        @keyframes bounce {
            0%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <img src="../images/icon128.png" alt="爱嘉客服" class="logo">
        <h1>爱嘉客服 - 拼多多AI助手</h1>
        <p>正在跳转到拼多多商家客服聊天窗口，请稍候...</p>
        <div class="loading">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
        </div>
    </div>

    <script>
        // 页面加载完成后自动跳转
        window.onload = function() {
            // 延迟1秒后跳转，让用户能看到跳转页面
            setTimeout(function() {
                window.location.href = "https://mms.pinduoduo.com/chat-merchant/index.html#/";
            }, 1000);
        };
    </script>
</body>
</html> 