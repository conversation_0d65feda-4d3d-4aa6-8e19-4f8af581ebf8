# 无限循环问题修复说明

## 问题分析

从日志可以看出系统陷入了无限循环，主要问题：

1. **重复检测过于严格**：一旦设置了会话ID，就永远不会再发送请求
2. **没有超时重置机制**：变量永远不会被清除
3. **格式化逻辑不一致**：两套不同的格式化逻辑导致混乱

### 问题日志分析

```
[11:54:13] [下单数检测] 成功获取到订单项，开始提取详细信息
[11:54:13] [下单数处理] 检测到相同的会话ID，跳过重复发送
[11:54:13] checkUnreadMessages 处理完成，重置状态
[11:54:14] [监听消息]开始检查未读消息
// 然后又开始新的循环...
```

## 修复方案

### 1. 改进重复检测逻辑

**修复前：**
```javascript
// 永久性检测，一旦设置就永远不会重置
if (lastOrderRequestConversationId === conversationId && lastOrderRequestUserId === userInfo) {
    debug('[下单数处理] 检测到相同的会话ID，跳过重复发送');
    return;
}
```

**修复后：**
```javascript
// 添加时间窗口检测
const now = Date.now();
const isRecentRequest = lastOrderRequestTime && (now - lastOrderRequestTime) < ORDER_REQUEST_TIMEOUT;
const isSameRequest = lastOrderRequestConversationId === conversationId && lastOrderRequestUserId === userInfo;

if (isRecentRequest && isSameRequest) {
    debug('[下单数处理] 检测到重复请求，跳过发送');
    debug(`[下单数处理] 上次请求时间: ${new Date(lastOrderRequestTime).toLocaleTimeString()}, 当前时间: ${new Date(now).toLocaleTimeString()}`);
    return;
}
```

### 2. 添加超时机制

```javascript
// 全局变量增加时间戳
let lastOrderRequestTime = null;
const ORDER_REQUEST_TIMEOUT = 30000; // 30秒超时
```

### 3. 统一格式化逻辑

**修复前：** 使用复杂的键值映射和过滤逻辑
**修复后：** 使用简单直接的格式化，与 `common.js` 中的格式保持一致

```javascript
formattedOrderInfo = `订单数：${orderInfo.orderCount || '未知'}`;
if (orderInfo.orderStatus) {
    formattedOrderInfo += `\n订单状态：${orderInfo.orderStatus}`;
}
if (orderInfo.goodsName) {
    formattedOrderInfo += `\n商品名：${orderInfo.goodsName}`;
}
// ... 其他字段
```

### 4. 健康检查清理机制

在系统健康检查中添加过期状态清理：

```javascript
// 检查订单请求状态是否需要清理
if (lastOrderRequestTime) {
    const orderRequestAge = Date.now() - lastOrderRequestTime;
    if (orderRequestAge > ORDER_REQUEST_TIMEOUT) {
        needsRepair = true;
        repairs.push('订单请求状态过期');
    }
}

// 在修复操作中清理过期状态
if (lastOrderRequestTime && (Date.now() - lastOrderRequestTime) > ORDER_REQUEST_TIMEOUT) {
    debug('清理过期的订单请求状态');
    lastOrderRequestConversationId = null;
    lastOrderRequestUserId = null;
    lastOrderRequestTime = null;
}
```

## 修复的文件

1. **AIPDD-DOM/src/content/content.js**
   - 改进重复检测逻辑
   - 添加超时机制
   - 统一格式化逻辑
   - 增强健康检查清理

## 预期效果

修复后的流程：

1. ✅ 检测到下单数买家
2. ✅ 成功获取订单信息
3. ✅ 格式化订单信息（统一格式）
4. ✅ 发送到DIFY（不会被重复检测阻止）
5. ✅ 处理完成，不再循环

### 日志示例

修复后应该看到：
```
[下单数检测] 成功获取到订单项，开始提取详细信息
[下单数处理] 开始发送新的订单信息请求
[下单数处理] 格式化后的订单信息: 订单数：1
订单状态：未发货，退款成功
商品名：浴室清洁剂瓷砖玻璃水龙头清洗剂洗手盆强力去污除垢
实付金额：¥10.68
数量：1
备注：25绿色的
下单时间：2025/07/04 15:10
[下单数处理] DIFY请求发送成功
checkUnreadMessages 处理完成，重置状态
```

## 测试方法

1. **重新加载扩展程序**
2. **在有订单的买家对话中测试**
3. **观察日志**：
   - 应该只处理一次订单信息
   - 不应该出现无限循环
   - 应该看到格式化的订单信息发送到DIFY

## 容错机制

1. **时间窗口保护**：30秒内相同请求会被跳过
2. **健康检查清理**：每30秒检查并清理过期状态
3. **格式化容错**：如果格式化失败，使用最基本的订单数信息

## 注意事项

- 修复后第一次测试可能需要等待30秒让旧状态过期
- 如果仍有问题，可以刷新页面重置所有状态
- 系统会自动在健康检查时清理过期的请求状态
