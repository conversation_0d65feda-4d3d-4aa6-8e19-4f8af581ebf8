// 测试后台脚本是否正常工作的脚本
console.log('=== 测试后台脚本 ===');

// 1. 测试后台脚本连接
function testBackgroundConnection() {
    console.log('\n1. 测试后台脚本连接...');
    
    return new Promise((resolve) => {
        try {
            chrome.runtime.sendMessage({
                type: 'ping'
            }, response => {
                if (chrome.runtime.lastError) {
                    console.log('❌ 后台脚本连接失败:', chrome.runtime.lastError.message);
                    resolve(false);
                    return;
                }
                
                console.log('✅ 后台脚本连接成功');
                console.log('响应:', response);
                resolve(true);
            });
        } catch (error) {
            console.log('❌ 发送消息失败:', error.message);
            resolve(false);
        }
    });
}

// 2. 测试DIFY配置获取
function testDifyConfigRetrieval() {
    console.log('\n2. 测试DIFY配置获取...');
    
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({
            type: 'getInitialDifyConfig'
        }, response => {
            if (chrome.runtime.lastError) {
                console.log('❌ 获取DIFY配置失败:', chrome.runtime.lastError.message);
                resolve(null);
                return;
            }
            
            if (response && response.success && response.data) {
                console.log('✅ DIFY配置获取成功');
                console.log('配置详情:', {
                    apiUrl: response.data.apiUrl,
                    hasApiKey: !!response.data.apiKey,
                    apiKeyLength: response.data.apiKey ? response.data.apiKey.length : 0
                });
                resolve(response.data);
            } else {
                console.log('❌ DIFY配置响应无效:', response);
                resolve(null);
            }
        });
    });
}

// 3. 测试简单DIFY请求
function testSimpleDifyRequest(config) {
    console.log('\n3. 测试简单DIFY请求...');
    
    if (!config || !config.apiUrl || !config.apiKey) {
        console.log('❌ DIFY配置不完整，跳过请求测试');
        return Promise.resolve(false);
    }
    
    const testData = {
        inputs: {},
        query: '测试消息：请回复"测试成功"',
        response_mode: "blocking",
        user: 'test_user_' + Date.now(),
        requestId: Date.now(),
        _isBackgroundOrderInfoRequest: false
    };
    
    console.log('发送测试请求:', testData);
    
    return new Promise((resolve) => {
        const startTime = Date.now();
        
        chrome.runtime.sendMessage({
            type: 'difyRequest',
            data: testData
        }, response => {
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            console.log(`请求耗时: ${duration}ms`);
            
            if (chrome.runtime.lastError) {
                console.log('❌ DIFY请求失败:', chrome.runtime.lastError.message);
                resolve(false);
                return;
            }
            
            console.log('DIFY请求响应:', response);
            
            if (response && response.success) {
                console.log('✅ DIFY请求成功');
                if (response.data && response.data.answer) {
                    console.log('AI回复:', response.data.answer);
                }
                resolve(true);
            } else {
                console.log('❌ DIFY请求失败:', response);
                resolve(false);
            }
        });
    });
}

// 4. 测试订单信息请求
function testOrderInfoRequest(config) {
    console.log('\n4. 测试订单信息请求...');
    
    if (!config || !config.apiUrl || !config.apiKey) {
        console.log('❌ DIFY配置不完整，跳过订单信息测试');
        return Promise.resolve(false);
    }
    
    const orderInfo = `订单数：1
订单状态：未发货，退款成功
商品名：浴室清洁剂瓷砖玻璃水龙头清洗剂洗手盆强力去污除垢
实付金额：¥10.68
数量：1
备注：25绿色的
下单时间：2025/07/04 15:10`;

    const requestContent = `这是一个下单数买家的订单信息:\n${orderInfo}\n记录这个信息，不需要回复买家。`;
    
    const orderData = {
        inputs: {},
        query: requestContent,
        response_mode: "blocking",
        user: '6484435977599_嘉维仕_小嘉_1***',
        requestId: Date.now(),
        _isBackgroundOrderInfoRequest: true
    };
    
    console.log('发送订单信息请求...');
    console.log('请求内容:', requestContent);
    
    return new Promise((resolve) => {
        const startTime = Date.now();
        
        chrome.runtime.sendMessage({
            type: 'difyRequest',
            data: orderData
        }, response => {
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            console.log(`订单信息请求耗时: ${duration}ms`);
            
            if (chrome.runtime.lastError) {
                console.log('❌ 订单信息请求失败:', chrome.runtime.lastError.message);
                resolve(false);
                return;
            }
            
            console.log('订单信息请求响应:', response);
            
            if (response && response.success) {
                console.log('✅ 订单信息请求成功');
                if (response.data && response.data.answer) {
                    console.log('AI处理结果:', response.data.answer);
                }
                resolve(true);
            } else {
                console.log('❌ 订单信息请求失败:', response);
                resolve(false);
            }
        });
    });
}

// 5. 主测试流程
async function runBackgroundTests() {
    console.log('开始测试后台脚本...\n');
    
    // 测试1：后台脚本连接
    const connectionOk = await testBackgroundConnection();
    if (!connectionOk) {
        console.log('\n❌ 后台脚本连接失败，请检查扩展程序是否正常加载');
        console.log('修复建议：');
        console.log('1. 重新加载扩展程序');
        console.log('2. 检查manifest.json配置');
        console.log('3. 查看扩展程序错误日志');
        return;
    }
    
    // 测试2：DIFY配置
    const config = await testDifyConfigRetrieval();
    if (!config) {
        console.log('\n❌ DIFY配置获取失败');
        return;
    }
    
    // 测试3：简单请求
    const simpleRequestOk = await testSimpleDifyRequest(config);
    if (!simpleRequestOk) {
        console.log('\n❌ 简单DIFY请求失败，请检查DIFY配置和网络连接');
        return;
    }
    
    // 测试4：订单信息请求
    const orderRequestOk = await testOrderInfoRequest(config);
    if (orderRequestOk) {
        console.log('\n✅ 所有测试通过！后台脚本工作正常');
        console.log('现在可以重新测试订单检测功能了');
    } else {
        console.log('\n❌ 订单信息请求失败');
        console.log('请检查后台脚本日志获取更多信息');
    }
}

// 6. 提供手动测试函数
window.testBackgroundScript = runBackgroundTests;
window.testDifyConnection = testBackgroundConnection;
window.testDifyConfig = testDifyConfigRetrieval;

// 运行测试
runBackgroundTests().catch(error => {
    console.error('测试过程中出现错误:', error);
});

console.log('\n=== 后台脚本测试已启动 ===');
console.log('如果需要重新测试，可以运行: testBackgroundScript()');
console.log('如果需要单独测试连接，可以运行: testDifyConnection()');
console.log('如果需要单独测试配置，可以运行: testDifyConfig()');
