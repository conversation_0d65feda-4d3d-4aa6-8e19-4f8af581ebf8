// 专门测试标签点击功能的脚本
console.log('=== 测试标签点击功能 ===');

// 测试标签文本匹配
function testTabTextMatching() {
    console.log('\n1. 测试标签文本匹配:');
    
    const barItems = document.querySelectorAll('.bar-box.four-tab .bar-item');
    console.log('找到标签数量:', barItems.length);
    
    barItems.forEach((item, index) => {
        const originalText = item.textContent;
        const trimmedText = item.textContent.trim();
        console.log(`标签${index}:`);
        console.log(`  原始文本: "${originalText}"`);
        console.log(`  去空格后: "${trimmedText}"`);
        console.log(`  匹配最新订单: ${trimmedText === '最新订单'}`);
        console.log(`  是否选中: ${item.classList.contains('bar-select')}`);
    });
}

// 测试点击最新订单标签
async function testClickLatestOrder() {
    console.log('\n2. 测试点击最新订单标签:');
    
    const barItems = document.querySelectorAll('.bar-box.four-tab .bar-item');
    let foundLatestOrder = false;
    
    for (let i = 0; i < barItems.length; i++) {
        const item = barItems[i];
        const itemText = item.textContent.trim();
        
        if (itemText === '最新订单') {
            console.log('找到最新订单标签，准备点击...');
            foundLatestOrder = true;
            
            // 点击标签
            item.click();
            console.log('已点击最新订单标签');
            
            // 等待页面响应
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 检查是否成功切换
            const isSelected = item.classList.contains('bar-select');
            console.log('标签是否被选中:', isSelected);
            
            break;
        }
    }
    
    if (!foundLatestOrder) {
        console.log('未找到最新订单标签');
        return false;
    }
    
    return true;
}

// 测试二级标签
async function testSecondaryTabs() {
    console.log('\n3. 测试二级标签:');
    
    // 等待二级标签出现
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const secondaryTabs = document.querySelectorAll('.order-panel-second-bar');
    console.log('找到二级标签数量:', secondaryTabs.length);
    
    secondaryTabs.forEach((tab, index) => {
        const originalText = tab.textContent;
        const trimmedText = tab.textContent.trim();
        console.log(`二级标签${index}:`);
        console.log(`  原始文本: "${originalText}"`);
        console.log(`  去空格后: "${trimmedText}"`);
        console.log(`  匹配个人订单: ${trimmedText === '个人订单'}`);
        console.log(`  是否选中: ${tab.classList.contains('bar-select')}`);
    });
    
    // 点击个人订单
    for (const tab of secondaryTabs) {
        if (tab.textContent.trim() === '个人订单') {
            console.log('找到个人订单标签，准备点击...');
            tab.click();
            console.log('已点击个人订单标签');
            
            // 等待页面响应
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const isSelected = tab.classList.contains('bar-select');
            console.log('个人订单标签是否被选中:', isSelected);
            
            return true;
        }
    }
    
    console.log('未找到个人订单标签');
    return false;
}

// 测试订单列表
async function testOrderList() {
    console.log('\n4. 测试订单列表:');
    
    // 等待订单列表加载
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const orderList = document.querySelector('.order-item-list');
    console.log('订单列表容器存在:', !!orderList);
    
    if (orderList) {
        const orderItems = orderList.querySelectorAll('.order-item');
        console.log('订单项数量:', orderItems.length);
        
        if (orderItems.length > 0) {
            const firstOrder = orderItems[0];
            console.log('第一个订单信息:');
            
            // 提取订单信息
            const orderStatus = firstOrder.querySelector('.title-status')?.textContent.trim() || '未找到';
            const orderNumber = firstOrder.querySelector('.order-sn')?.textContent.trim() || '未找到';
            const goodsNum = firstOrder.querySelector('.goods-num')?.textContent.trim() || '未找到';
            const goodsPrice = firstOrder.querySelector('.goods-price')?.textContent.trim() || '未找到';
            const amountValue = firstOrder.querySelector('.amount-value')?.textContent.trim() || '未找到';
            const remarkNote = firstOrder.querySelector('.remark-note')?.textContent.trim() || '未找到';
            
            console.log('  - 订单状态:', orderStatus);
            console.log('  - 订单编号:', orderNumber);
            console.log('  - 商品数量:', goodsNum);
            console.log('  - 商品价格:', goodsPrice);
            console.log('  - 实收金额:', amountValue);
            console.log('  - 订单备注:', remarkNote);
            
            return {
                orderStatus,
                orderNumber,
                goodsNum,
                goodsPrice,
                amountValue,
                remarkNote
            };
        } else {
            console.log('订单列表为空');
        }
    }
    
    return null;
}

// 运行完整测试
async function runFullTest() {
    try {
        testTabTextMatching();
        
        const clickedLatestOrder = await testClickLatestOrder();
        if (!clickedLatestOrder) {
            console.log('无法点击最新订单标签，测试终止');
            return;
        }
        
        const clickedPersonalOrder = await testSecondaryTabs();
        if (!clickedPersonalOrder) {
            console.log('无法点击个人订单标签，测试终止');
            return;
        }
        
        const orderInfo = await testOrderList();
        if (orderInfo) {
            console.log('\n=== 测试成功 ===');
            console.log('成功获取到订单信息:', orderInfo);
        } else {
            console.log('\n=== 测试失败 ===');
            console.log('无法获取订单信息');
        }
        
    } catch (error) {
        console.error('测试过程中出现错误:', error);
    }
}

// 执行测试
runFullTest();
