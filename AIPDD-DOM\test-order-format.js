// 测试订单信息格式化的脚本
console.log('=== 测试订单信息格式化 ===');

// 模拟订单信息（基于您提供的日志数据）
const mockOrderInfo = {
    orderStatus: '未发货，退款成功',
    orderNumber: '250704-650232491641407',
    orderTime: '2025/07/04 15:10',
    goodsPrice: '￥10.68',
    goodsNum: '1',
    remarkNote: '25绿色的',
    orderCount: '1',
    goodsName: '浴室清洁剂瓷砖玻璃水龙头清洗剂洗手盆强力去污除垢',
    amountValue: '¥10.68'
};

// 格式化订单信息的函数（复制自common.js）
function formatOrderInfo(orderInfo) {
    if (!orderInfo) return '';
    
    let formattedText = '';
    
    // 按照指定格式组织订单信息
    // 订单数：*
    if (orderInfo.orderCount) {
        formattedText += `订单数：${orderInfo.orderCount}\n`;
    }
    
    // 订单状态：**
    if (orderInfo.orderStatus) {
        formattedText += `订单状态：${orderInfo.orderStatus}\n`;
    }
    
    // 商品名：***
    if (orderInfo.goodsName) {
        formattedText += `商品名：${orderInfo.goodsName}\n`;
    }
    
    // 实付金额：**
    if (orderInfo.amountValue) {
        formattedText += `实付金额：${orderInfo.amountValue}\n`;
    } else if (orderInfo.goodsPrice) {
        // 如果没有实付金额，使用商品价格作为备选
        formattedText += `实付金额：${orderInfo.goodsPrice}\n`;
    }
    
    // 数量：**
    if (orderInfo.goodsNum) {
        formattedText += `数量：${orderInfo.goodsNum}\n`;
    }
    
    // 备注：**
    if (orderInfo.remarkNote) {
        formattedText += `备注：${orderInfo.remarkNote}\n`;
    }
    
    // 下单时间：**
    if (orderInfo.orderTime) {
        formattedText += `下单时间：${orderInfo.orderTime}\n`;
    }
    
    return formattedText.trim(); // 去除末尾的换行符
}

// 测试格式化功能
function testFormatting() {
    console.log('\n1. 原始订单信息:');
    console.log(mockOrderInfo);
    
    console.log('\n2. 格式化后的订单信息:');
    const formatted = formatOrderInfo(mockOrderInfo);
    console.log(formatted);
    
    console.log('\n3. 格式化后的订单信息（带引号显示）:');
    console.log(`"${formatted}"`);
    
    console.log('\n4. 检查格式是否符合要求:');
    const lines = formatted.split('\n');
    const expectedFields = ['订单数', '订单状态', '商品名', '实付金额', '数量', '备注', '下单时间'];
    
    expectedFields.forEach(field => {
        const found = lines.some(line => line.startsWith(`${field}：`));
        console.log(`  ${field}：${found ? '✓' : '✗'}`);
    });
    
    console.log('\n5. 完整的DIFY请求内容预览:');
    const requestContent = `这是一个下单数买家的订单信息:\n${formatted}\n请为这个客户提供一个适合的回复。`;
    console.log(requestContent);
}

// 测试不同的订单信息场景
function testDifferentScenarios() {
    console.log('\n=== 测试不同场景 ===');
    
    // 场景1：缺少商品名称
    console.log('\n场景1：缺少商品名称');
    const orderWithoutGoodsName = { ...mockOrderInfo };
    delete orderWithoutGoodsName.goodsName;
    console.log(formatOrderInfo(orderWithoutGoodsName));
    
    // 场景2：缺少实付金额，只有商品价格
    console.log('\n场景2：缺少实付金额，只有商品价格');
    const orderWithoutAmountValue = { ...mockOrderInfo };
    delete orderWithoutAmountValue.amountValue;
    console.log(formatOrderInfo(orderWithoutAmountValue));
    
    // 场景3：缺少备注
    console.log('\n场景3：缺少备注');
    const orderWithoutRemark = { ...mockOrderInfo };
    delete orderWithoutRemark.remarkNote;
    console.log(formatOrderInfo(orderWithoutRemark));
    
    // 场景4：最小信息集
    console.log('\n场景4：最小信息集');
    const minimalOrder = {
        orderCount: '1',
        goodsName: '测试商品',
        amountValue: '¥10.00'
    };
    console.log(formatOrderInfo(minimalOrder));
}

// 运行测试
testFormatting();
testDifferentScenarios();

console.log('\n=== 测试完成 ===');
