// 测试DIFY配置的脚本
console.log('=== 测试DIFY配置 ===');

// 测试获取DIFY配置
function testDifyConfig() {
    console.log('\n1. 测试获取DIFY配置:');
    
    // 发送消息到后台脚本获取配置
    chrome.runtime.sendMessage({
        type: 'getInitialDifyConfig'
    }, response => {
        console.log('DIFY配置响应:', response);
        
        if (response && response.success) {
            const config = response.data;
            console.log('DIFY配置详情:');
            console.log('  - API URL:', config.apiUrl);
            console.log('  - 有API Key:', !!config.apiKey);
            console.log('  - API Key长度:', config.apiKey ? config.apiKey.length : 0);
            console.log('  - API Key前缀:', config.apiKey ? config.apiKey.substring(0, 10) + '...' : '无');
            
            // 检查配置是否完整
            if (!config.apiUrl) {
                console.error('❌ API URL 未配置');
            } else {
                console.log('✅ API URL 已配置');
            }
            
            if (!config.apiKey) {
                console.error('❌ API Key 未配置');
            } else {
                console.log('✅ API Key 已配置');
            }
            
            // 测试发送简单请求
            if (config.apiUrl && config.apiKey) {
                testDifyRequest(config);
            } else {
                console.error('❌ DIFY配置不完整，无法测试请求');
            }
        } else {
            console.error('❌ 获取DIFY配置失败:', response);
        }
    });
}

// 测试发送DIFY请求
function testDifyRequest(config) {
    console.log('\n2. 测试发送DIFY请求:');
    
    const testData = {
        inputs: {},
        query: '测试消息：这是一个测试请求',
        response_mode: "blocking",
        user: 'test_user_123',
        requestId: Date.now(),
        _isBackgroundOrderInfoRequest: false
    };
    
    console.log('发送测试请求:', testData);
    
    chrome.runtime.sendMessage({
        type: 'difyRequest',
        data: testData
    }, response => {
        console.log('DIFY请求响应:', response);
        
        if (response && response.success) {
            console.log('✅ DIFY请求成功');
            console.log('响应数据:', response.data);
            
            if (response.data && response.data.answer) {
                console.log('✅ 收到AI回复:', response.data.answer);
            } else {
                console.log('⚠️ 响应中没有AI回复');
            }
            
            if (response.data && response.data.conversation_id) {
                console.log('✅ 收到会话ID:', response.data.conversation_id);
            } else {
                console.log('⚠️ 响应中没有会话ID');
            }
        } else {
            console.error('❌ DIFY请求失败:', response);
            
            if (response && response.error) {
                console.error('错误详情:', response.error);
            }
        }
    });
}

// 测试订单信息格式化
function testOrderInfoFormatting() {
    console.log('\n3. 测试订单信息格式化:');
    
    const mockOrderInfo = {
        orderStatus: '未发货，退款成功',
        orderNumber: '250704-650232491641407',
        orderTime: '2025/07/04 15:10',
        goodsPrice: '￥10.68',
        goodsNum: '1',
        remarkNote: '25绿色的',
        orderCount: '1',
        goodsName: '浴室清洁剂瓷砖玻璃水龙头清洗剂洗手盆强力去污除垢',
        amountValue: '¥10.68'
    };
    
    // 格式化订单信息
    let formattedOrderInfo = `订单数：${mockOrderInfo.orderCount || '未知'}`;
    
    if (mockOrderInfo.orderStatus) {
        formattedOrderInfo += `\n订单状态：${mockOrderInfo.orderStatus}`;
    }
    if (mockOrderInfo.goodsName) {
        formattedOrderInfo += `\n商品名：${mockOrderInfo.goodsName}`;
    }
    if (mockOrderInfo.amountValue) {
        formattedOrderInfo += `\n实付金额：${mockOrderInfo.amountValue}`;
    } else if (mockOrderInfo.goodsPrice) {
        formattedOrderInfo += `\n实付金额：${mockOrderInfo.goodsPrice}`;
    }
    if (mockOrderInfo.goodsNum) {
        formattedOrderInfo += `\n数量：${mockOrderInfo.goodsNum}`;
    }
    if (mockOrderInfo.remarkNote) {
        formattedOrderInfo += `\n备注：${mockOrderInfo.remarkNote}`;
    }
    if (mockOrderInfo.orderTime) {
        formattedOrderInfo += `\n下单时间：${mockOrderInfo.orderTime}`;
    }
    
    console.log('格式化后的订单信息:');
    console.log(formattedOrderInfo);
    
    // 测试发送订单信息到DIFY
    const requestContent = `这是一个下单数买家的订单信息:\n${formattedOrderInfo}\n记录这个信息，不需要回复买家。`;
    
    console.log('\n完整的DIFY请求内容:');
    console.log(requestContent);
    
    return { formattedOrderInfo, requestContent };
}

// 测试发送订单信息到DIFY
function testOrderInfoToDify() {
    console.log('\n4. 测试发送订单信息到DIFY:');
    
    const { requestContent } = testOrderInfoFormatting();
    
    const orderData = {
        inputs: {},
        query: requestContent,
        response_mode: "blocking",
        user: '6484435977599_嘉维仕_小嘉_1***',
        requestId: Date.now(),
        _isBackgroundOrderInfoRequest: true
    };
    
    console.log('发送订单信息请求:', orderData);
    
    chrome.runtime.sendMessage({
        type: 'difyRequest',
        data: orderData
    }, response => {
        console.log('订单信息请求响应:', response);
        
        if (response && response.success) {
            console.log('✅ 订单信息发送成功');
            console.log('AI处理结果:', response.data);
        } else {
            console.error('❌ 订单信息发送失败:', response);
        }
    });
}

// 运行所有测试
function runAllTests() {
    console.log('开始运行DIFY配置测试...');
    
    // 检查是否在扩展程序环境中
    if (typeof chrome === 'undefined' || !chrome.runtime) {
        console.error('❌ 不在Chrome扩展程序环境中，无法测试');
        return;
    }
    
    testDifyConfig();
    
    // 延迟执行其他测试，等待配置获取完成
    setTimeout(() => {
        testOrderInfoFormatting();
    }, 2000);
    
    setTimeout(() => {
        testOrderInfoToDify();
    }, 4000);
}

// 执行测试
runAllTests();

console.log('\n=== 测试脚本已启动 ===');
console.log('请查看上方的测试结果，特别注意：');
console.log('1. DIFY配置是否完整');
console.log('2. 测试请求是否成功');
console.log('3. 订单信息格式是否正确');
console.log('4. 后台脚本是否有错误日志');
