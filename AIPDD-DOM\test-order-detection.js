// 测试订单检测功能的脚本
// 在浏览器控制台中运行此脚本来测试订单信息提取

console.log('=== 开始测试订单检测功能 ===');

// 测试页面结构调试函数
function testDebugPageStructure() {
    console.log('\n1. 测试页面结构调试:');

    // 检查标签栏（根据实际页面结构）
    const barBox = document.querySelector('.bar-box.four-tab');
    console.log('标签栏存在:', !!barBox);

    if (barBox) {
        console.log('标签栏类名:', barBox.className);
        const barItems = barBox.querySelectorAll('.bar-item');
        console.log('标签项数量:', barItems.length);

        barItems.forEach((item, index) => {
            console.log(`标签${index}:`, item.textContent.trim());
        });
    }

    // 检查订单二级面板
    const orderPanelBars = document.querySelectorAll('.order-panel-second-bar');
    console.log('订单二级面板数量:', orderPanelBars.length);

    orderPanelBars.forEach((panel, index) => {
        console.log(`二级面板${index}:`, panel.textContent.trim());
    });

    // 检查订单列表
    const orderList = document.querySelector('.order-item-list');
    console.log('订单列表存在:', !!orderList);

    if (orderList) {
        const orderItems = orderList.querySelectorAll('.order-item');
        console.log('订单项数量:', orderItems.length);

        if (orderItems.length > 0) {
            const firstOrder = orderItems[0];
            console.log('第一个订单信息:');
            console.log('  - 订单状态:', firstOrder.querySelector('.title-status')?.textContent.trim() || '未找到');
            console.log('  - 订单编号:', firstOrder.querySelector('.order-sn')?.textContent.trim() || '未找到');
            console.log('  - 商品数量:', firstOrder.querySelector('.goods-num')?.textContent.trim() || '未找到');
            console.log('  - 实收金额:', firstOrder.querySelector('.amount-value')?.textContent.trim() || '未找到');
        }
    }
}

// 测试订单信息提取
function testOrderInfoExtraction() {
    console.log('\n2. 测试订单信息提取:');
    
    // 测试不同的选择器
    const selectors = [
        '.order-item',
        '.order-item-list .order-item',
        '.right-panel-container .order-item',
        '.right-panel-container .order-item-list .order-item'
    ];
    
    selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        console.log(`选择器 "${selector}" 找到元素数量:`, elements.length);
        
        if (elements.length > 0) {
            const firstElement = elements[0];
            console.log('第一个元素的信息:');
            console.log('  - 订单状态:', firstElement.querySelector('.order-status .title-status')?.textContent.trim() || '未找到');
            console.log('  - 订单编号:', firstElement.querySelector('.order-number .order-sn')?.textContent.trim() || '未找到');
            console.log('  - 商品数量:', firstElement.querySelector('.goods-num')?.textContent.replace('x', '').trim() || '未找到');
            console.log('  - 商品价格:', firstElement.querySelector('.goods-price')?.textContent.trim() || '未找到');
        }
    });
}

// 测试聊天标签提取
function testChatTagExtraction() {
    console.log('\n3. 测试聊天标签提取:');
    
    const chatTags = document.querySelectorAll('.chat-tag');
    console.log('找到聊天标签数量:', chatTags.length);
    
    chatTags.forEach((tag, index) => {
        console.log(`标签${index}:`, tag.textContent);
        const match = tag.textContent.match(/下单数\((\d+)\)/);
        if (match) {
            console.log(`  -> 提取到下单数: ${match[1]}`);
        }
    });
}

// 测试标签栏点击
async function testTabClicking() {
    console.log('\n4. 测试标签栏点击:');

    // 查找最新订单标签
    const barItems = document.querySelectorAll('.bar-box.four-tab .bar-item');
    console.log('找到标签数量:', barItems.length);
    
    for (let i = 0; i < barItems.length; i++) {
        const item = barItems[i];
        const itemText = item.textContent.trim();
        console.log(`标签${i}:`, `"${itemText}"`);

        if (itemText === '最新订单') {
            console.log('找到最新订单标签，准备点击...');
            item.click();
            
            // 等待页面响应
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 检查二级标签
            const secondBarItems = document.querySelectorAll('.order-panel-second-bar');
            console.log('二级标签数量:', secondBarItems.length);
            
            secondBarItems.forEach((secondItem, index) => {
                console.log(`二级标签${index}:`, secondItem.textContent);
            });
            
            break;
        }
    }
}

// 运行所有测试
async function runAllTests() {
    testDebugPageStructure();
    testOrderInfoExtraction();
    testChatTagExtraction();
    await testTabClicking();
    
    console.log('\n=== 测试完成 ===');
    console.log('请检查上述输出，找出问题所在。');
}

// 执行测试
runAllTests().catch(console.error);
