// 快速修复订单请求问题的脚本
console.log('=== 快速修复订单请求问题 ===');

// 1. 清除重复检测状态
function clearOrderRequestState() {
    console.log('1. 清除重复检测状态...');
    
    if (typeof window.clearOrderRequestState === 'function') {
        window.clearOrderRequestState();
        console.log('✅ 重复检测状态已清除');
    } else {
        console.log('⚠️ 清除函数不可用，手动清除...');
        // 手动清除（如果函数不可用）
        if (typeof lastOrderRequestConversationId !== 'undefined') {
            lastOrderRequestConversationId = null;
            lastOrderRequestUserId = null;
            lastOrderRequestTime = null;
            console.log('✅ 手动清除完成');
        } else {
            console.log('⚠️ 变量不可访问，可能需要重新加载扩展程序');
        }
    }
}

// 2. 检查扩展程序上下文
function checkExtensionContext() {
    console.log('\n2. 检查扩展程序上下文...');
    
    try {
        if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
            console.log('✅ 扩展程序上下文正常');
            console.log('扩展程序ID:', chrome.runtime.id);
            return true;
        } else {
            console.log('❌ 扩展程序上下文无效');
            return false;
        }
    } catch (error) {
        console.log('❌ 扩展程序上下文检查失败:', error.message);
        return false;
    }
}

// 3. 测试DIFY配置
function testDifyConfig() {
    console.log('\n3. 测试DIFY配置...');
    
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({
            type: 'getInitialDifyConfig'
        }, response => {
            if (chrome.runtime.lastError) {
                console.log('❌ 获取DIFY配置失败:', chrome.runtime.lastError.message);
                resolve(false);
                return;
            }
            
            if (response && response.success && response.data) {
                const config = response.data;
                console.log('✅ DIFY配置获取成功');
                console.log('  - API URL:', config.apiUrl);
                console.log('  - 有API Key:', !!config.apiKey);
                console.log('  - API Key长度:', config.apiKey ? config.apiKey.length : 0);
                
                if (config.apiUrl && config.apiKey) {
                    console.log('✅ DIFY配置完整');
                    resolve(true);
                } else {
                    console.log('❌ DIFY配置不完整');
                    resolve(false);
                }
            } else {
                console.log('❌ DIFY配置响应无效:', response);
                resolve(false);
            }
        });
    });
}

// 4. 发送测试请求
function sendTestRequest() {
    console.log('\n4. 发送测试订单信息请求...');
    
    const testOrderInfo = `订单数：1
订单状态：未发货，退款成功
商品名：浴室清洁剂瓷砖玻璃水龙头清洗剂洗手盆强力去污除垢
实付金额：¥10.68
数量：1
备注：25绿色的
下单时间：2025/07/04 15:10`;

    const requestContent = `这是一个下单数买家的订单信息:\n${testOrderInfo}\n记录这个信息，不需要回复买家。`;
    
    const testData = {
        inputs: {},
        query: requestContent,
        response_mode: "blocking",
        user: '6484435977599_嘉维仕_小嘉_1***',
        requestId: Date.now(),
        _isBackgroundOrderInfoRequest: true
    };
    
    console.log('发送测试请求...');
    console.log('请求内容:', requestContent);
    
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({
            type: 'difyRequest',
            data: testData
        }, response => {
            if (chrome.runtime.lastError) {
                console.log('❌ 测试请求失败:', chrome.runtime.lastError.message);
                resolve(false);
                return;
            }
            
            console.log('测试请求响应:', response);
            
            if (response && response.success) {
                console.log('✅ 测试请求成功');
                if (response.data && response.data.answer) {
                    console.log('✅ 收到AI回复:', response.data.answer);
                }
                resolve(true);
            } else {
                console.log('❌ 测试请求失败:', response);
                resolve(false);
            }
        });
    });
}

// 5. 主修复流程
async function runQuickFix() {
    console.log('开始快速修复流程...\n');
    
    // 步骤1：清除重复检测状态
    clearOrderRequestState();
    
    // 步骤2：检查扩展程序上下文
    const contextOk = checkExtensionContext();
    if (!contextOk) {
        console.log('\n❌ 扩展程序上下文无效，请重新加载扩展程序！');
        console.log('修复步骤：');
        console.log('1. 打开 Chrome 扩展程序管理页面 (chrome://extensions/)');
        console.log('2. 找到 AIPDD 扩展程序');
        console.log('3. 点击刷新按钮重新加载');
        console.log('4. 刷新拼多多商家后台页面');
        console.log('5. 重新运行此脚本');
        return;
    }
    
    // 步骤3：测试DIFY配置
    const configOk = await testDifyConfig();
    if (!configOk) {
        console.log('\n❌ DIFY配置有问题，请检查配置！');
        return;
    }
    
    // 步骤4：发送测试请求
    const requestOk = await sendTestRequest();
    if (requestOk) {
        console.log('\n✅ 快速修复成功！');
        console.log('现在可以重新测试订单检测功能了。');
    } else {
        console.log('\n❌ 测试请求失败，请检查后台脚本日志。');
        console.log('调试步骤：');
        console.log('1. 打开扩展程序管理页面');
        console.log('2. 点击 AIPDD 扩展的"检查视图" → "背景页"');
        console.log('3. 查看控制台错误日志');
    }
}

// 6. 提供手动清除函数
window.quickFixOrderRequest = runQuickFix;
window.clearOrderState = clearOrderRequestState;

// 运行修复
runQuickFix().catch(error => {
    console.error('修复过程中出现错误:', error);
});

console.log('\n=== 快速修复脚本已启动 ===');
console.log('如果需要手动清除状态，可以运行: clearOrderState()');
console.log('如果需要重新运行修复，可以运行: quickFixOrderRequest()');
