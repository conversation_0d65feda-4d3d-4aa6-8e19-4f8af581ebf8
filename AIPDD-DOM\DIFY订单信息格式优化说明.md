# DIFY订单信息格式优化说明

## 优化内容

### 1. 订单信息格式标准化

根据要求，将发送到DIFY的订单信息格式统一为：

```
订单数：*
订单状态：**
商品名：***
实付金额：**
数量：**
备注：**
下单时间：**
```

### 2. 修改的文件

**文件：** `AIPDD-DOM/src/utils/common.js`

**函数：** `formatOrderInfo(orderInfo)`

### 3. 格式化逻辑

```javascript
export function formatOrderInfo(orderInfo) {
    if (!orderInfo) return '';
    
    let formattedText = '';
    
    // 订单数：*
    if (orderInfo.orderCount) {
        formattedText += `订单数：${orderInfo.orderCount}\n`;
    }
    
    // 订单状态：**
    if (orderInfo.orderStatus) {
        formattedText += `订单状态：${orderInfo.orderStatus}\n`;
    }
    
    // 商品名：***
    if (orderInfo.goodsName) {
        formattedText += `商品名：${orderInfo.goodsName}\n`;
    }
    
    // 实付金额：**（优先使用amountValue，备选goodsPrice）
    if (orderInfo.amountValue) {
        formattedText += `实付金额：${orderInfo.amountValue}\n`;
    } else if (orderInfo.goodsPrice) {
        formattedText += `实付金额：${orderInfo.goodsPrice}\n`;
    }
    
    // 数量：**
    if (orderInfo.goodsNum) {
        formattedText += `数量：${orderInfo.goodsNum}\n`;
    }
    
    // 备注：**
    if (orderInfo.remarkNote) {
        formattedText += `备注：${orderInfo.remarkNote}\n`;
    }
    
    // 下单时间：**
    if (orderInfo.orderTime) {
        formattedText += `下单时间：${orderInfo.orderTime}\n`;
    }
    
    return formattedText.trim();
}
```

### 4. 字段映射说明

| 格式要求 | 对应字段 | 备选字段 | 说明 |
|---------|---------|---------|------|
| 订单数 | `orderInfo.orderCount` | - | 从聊天标签或商品数量提取 |
| 订单状态 | `orderInfo.orderStatus` | - | 如"未发货，退款成功" |
| 商品名 | `orderInfo.goodsName` | - | 商品完整名称 |
| 实付金额 | `orderInfo.amountValue` | `orderInfo.goodsPrice` | 优先使用实收金额 |
| 数量 | `orderInfo.goodsNum` | - | 商品购买数量 |
| 备注 | `orderInfo.remarkNote` | - | 订单备注信息 |
| 下单时间 | `orderInfo.orderTime` | - | 如"2025/07/04 15:10" |

### 5. 示例输出

**输入数据：**
```javascript
{
    orderStatus: '未发货，退款成功',
    orderNumber: '250704-650232491641407',
    orderTime: '2025/07/04 15:10',
    goodsPrice: '￥10.68',
    goodsNum: '1',
    remarkNote: '25绿色的',
    orderCount: '1',
    goodsName: '浴室清洁剂瓷砖玻璃水龙头清洗剂洗手盆强力去污除垢',
    amountValue: '¥10.68'
}
```

**格式化输出：**
```
订单数：1
订单状态：未发货，退款成功
商品名：浴室清洁剂瓷砖玻璃水龙头清洗剂洗手盆强力去污除垢
实付金额：¥10.68
数量：1
备注：25绿色的
下单时间：2025/07/04 15:10
```

**完整DIFY请求内容：**
```
这是一个下单数买家的订单信息:
订单数：1
订单状态：未发货，退款成功
商品名：浴室清洁剂瓷砖玻璃水龙头清洗剂洗手盆强力去污除垢
实付金额：¥10.68
数量：1
备注：25绿色的
下单时间：2025/07/04 15:10
请为这个客户提供一个适合的回复。
```

### 6. 调试信息增强

在 `content.js` 中增加了详细的调试输出：

```javascript
debug('[下单数检测] 详细信息:');
debug('  - 订单状态:', orderStatus);
debug('  - 订单编号:', orderNumber);
debug('  - 下单时间:', orderTime);
debug('  - 商品价格:', goodsPrice);
debug('  - 商品数量:', goodsNum);
debug('  - 订单备注:', remarkNote);
debug('  - 下单数量:', orderCount);
debug('  - 商品名称:', goodsName);
debug('  - 实付金额:', amountValue);
```

以及格式化后的信息：
```javascript
debug('[下单数处理] 格式化后的订单信息:', formattedOrderInfo);
```

### 7. 测试方法

#### 方法1：查看控制台日志
重新加载扩展程序后，在有订单的买家对话中查看控制台，应该看到：
```
[下单数检测] 详细信息:
  - 商品名称: 浴室清洁剂瓷砖玻璃水龙头清洗剂洗手盆强力去污除垢
  - 实付金额: ¥10.68
[下单数处理] 格式化后的订单信息: 订单数：1
订单状态：未发货，退款成功
商品名：浴室清洁剂瓷砖玻璃水龙头清洗剂洗手盆强力去污除垢
实付金额：¥10.68
数量：1
备注：25绿色的
下单时间：2025/07/04 15:10
```

#### 方法2：使用测试脚本
在浏览器控制台中运行 `test-order-format.js` 脚本来验证格式化逻辑。

### 8. 容错处理

- 如果 `amountValue` 为空，自动使用 `goodsPrice` 作为实付金额
- 所有字段都进行空值检查，只有存在值的字段才会被包含在输出中
- 使用 `trim()` 去除多余的空白字符

### 9. 预期效果

优化后，DIFY将收到结构化、标准化的订单信息，能够更好地理解订单内容并生成相应的回复。格式清晰，便于AI模型解析和处理。
