/* 聊天窗口样式 */
.chat-window {
    position: fixed;
    top: 0;
    right: 0;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    z-index: 999999;
    display: flex;
    flex-direction: column;
}

/* 已回复标记样式 */
.replied-mark {
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background-color: #4CAF50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    z-index: 100;
    box-shadow: 0 1px 2px rgba(0,0,0,0.2);
    overflow: hidden;
}

/* 标题栏样式 */
.title-bar {
    padding: 10px;
    background: #2563eb;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 消息区域样式 */
.messages {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

/* 输入区域样式 */
.input-area {
    padding: 10px;
    border-top: 1px solid #e2e8f0;
    background: white;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    background: #2563eb;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn:hover {
    background: #1d4ed8;
}

/* 状态提示样式 */
.status {
    padding: 4px 8px;
    background: #f1f5f9;
    color: #475569;
    font-size: 12px;
    text-align: center;
}

/* 关键词管理相关样式 */
.keyword-management {
    flex-direction: column;
    align-items: stretch !important;
    background-color:rgb(243, 249, 255);
    border-radius: 6px;
    margin-bottom: 2px;
    margin-top: -10px
}

.option-label {
    padding-left: 10px;
    padding: 4px 4px;
}

.prompt-label {
    padding-left: 14px;
}

.main-switch-label {
    padding-left: 0;
    margin-top: 6px;
}

.keyword-input-group {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    margin-bottom: 4px;
    align-items: center;
}

.keyword-input-group input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.keyword-btn {
    padding: 8px 16px;
    background: #1677ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s;
    height: 32px;
}

.keyword-btn:hover {
    background: #1d4ed8;
}

.keyword-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    border-bottom: none;
}

.keyword-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 2px 8px;
    background: #f3f4f6;
    border-radius: 4px;
    font-size: 14px;
    margin: 6px 0 0 0;
}

.delete-keyword {
    background: none;
    border: none;
    color: #ef4444;
    cursor: pointer;
    padding: 0 4px;
    font-size: 16px;
    line-height: 1;
}

.delete-keyword:hover {
    color: #dc2626;
}

.delete-account {
    background: none;
    border: none;
    color: #ef4444;
    cursor: pointer;
    padding: 0 4px;
    font-size: 16px;
    line-height: 1;
}

.delete-account:hover {
    color: #dc2626;
}

/* 浮窗样式 */
.floating-window-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.floating-window {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    width: 90%;
    max-width: 480px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    animation: slideIn 0.3s ease;
    overflow: hidden;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.floating-window-header {
    padding: 16px 24px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8fafc;
}

.floating-window-header h2 {
    margin: 0;
    font-size: 18px;
    color: #1e293b;
    font-weight: 600;
}

.close-button {
    background: none;
    border: none;
    font-size: 24px;
    color: #64748b;
    cursor: pointer;
    padding: 4px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s;
}

.close-button:hover {
    background: #f1f5f9;
    color: #334155;
}

.floating-window-content {
    padding: 24px;
    overflow-y: auto;
    max-height: calc(90vh - 65px);
}

/* 设置页面样式 */
.settings-container {
    color: #334155;
}

.section {
    margin-bottom: 18px;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 12px;
}

.option-group {
    background:rgb(243, 249, 255);
    border-radius: 8px;
    padding: 6px;
    margin-bottom: 10px;
}

.option-item {
    padding: 6px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e2e8f0;
}

.option-item:has(.keyword-list) {
    border-bottom: none;
}

.option-item.keyword-management {
    display: flex;
    flex-direction: column;
    border-bottom: none;
}

.option-item:last-child {
    border-bottom: none;
}

.option-label {
    flex: 1;
}

.option-description {
    font-size: 13px;
    color: #64748b;
    margin-top: 4px;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 44px;
}

/* AI 设置相关样式 */
.settings-input {
    width: 420px;  /* 增加宽度以完整显示 API Key */
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    margin-top: 8px;
    font-family: monospace;  /* 使用等宽字体 */
    letter-spacing: 0.5px;  /* 增加字母间距，提高可读性 */
}

.settings-input:focus {
    border-color: #2563eb;
    outline: none;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.settings-input[type="number"] {
    width: 80px;  /* 数字输入框使用较小的宽度 */
    letter-spacing: normal;  /* 数字输入框使用正常字母间距 */
}

#aiSettingsSaveSuccess {
    display: none;
    text-align: center;
    color: #059669;
    margin-top: 12px;
    font-size: 14px;
}

/* 转人工设置保存成功提示样式，与AI设置保持一致 */
#transferFailMessageSaveSuccess {
    display: none;
    text-align: center;
    color: #059669;
    margin-top: 12px;
    font-size: 14px;
}

/* 保存按钮统一样式 */
.save-btn {
    background: #1677ff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    width: 100%;
    margin-top: 24px;
}

.save-btn:hover {
    background: #1d4ed8;
}

.save-success {
    display: none;
    font-size: 14px;
    text-align: center;
    margin-top: 12px;
    transition: opacity 0.3s ease;
    background-color:rgb(216, 255, 242);
}

.settings-saved-tip {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    color: #059669;
    padding: 6px 12px;
    border-radius: 4px;
}

/* 关于页面样式 */
.about-container {
    text-align: center;
    color: #334155;
}

.logo-section {
    margin-bottom: 24px;
}

.logo-section img {
    width: 80px;
    height: 80px;
    margin-bottom: 16px;
}

.logo-section h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 8px;
}

.version {
    font-size: 14px;
    color: #64748b;
}

.description-section {
    text-align: left;
    margin-bottom: 24px;
}

.description-section p {
    margin: 0 0 16px;
    line-height: 1.6;
}

.description-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.description-section li {
    padding: 8px 0 8px 24px;
    position: relative;
}

.description-section li:before {
    content: "•";
    color: #2563eb;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.contact-section {
    padding-top: 16px;
    border-top: 1px solid #e2e8f0;
    text-align: left;
}

.contact-section p {
    margin: 8px 0;
    color: #64748b;
}

/* 转人工失败处理方式相关样式 */
.transfer-fail-option {
    margin-bottom: 2px;
}

.transfer-fail-option-header {
    display: flex;
    align-items: center;
    margin-bottom: 2px;
}

/* 勾选框组件样式 */
.transfer-fail-checkbox {
    appearance: none;
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    background-color: white;
    margin-right: 5px;
    position: relative;
    cursor: pointer;
    vertical-align: middle;
}

.transfer-fail-checkbox:checked {
    background-color: #1677ff;
    border-color: #1677ff;
}

.transfer-fail-checkbox:checked::after {
    content: "";
    position: absolute;
    top: 2px;
    left: 5px;
    width: 4px;
    height: 7px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* 增加勾选项之间的间距 */
#transferFailOption1, #transferErrorOption1 {
    margin-right: 5px;
}

#transferFailOption2, #transferErrorOption2 {
    margin-left: 10px;
    margin-right: 5px;
}

.transfer-fail-option-header label {
    margin-right: 10px;
}

.transfer-fail-option-input {
    display: flex;
}

.transfer-fail-input {
    flex: 1;
    height: 32px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 0 10px;
}

.transfer-fail-btn {
    margin-left: 10px;
    height: 32px;
    background-color: #1677ff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0 15px;
    cursor: pointer;
}

.transfer-fail-btn:hover {
    background-color: #4096ff;
}

.transfer-fail-radio {
    margin-right: 8px;
}