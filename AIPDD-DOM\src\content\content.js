const VERSION = 'v1.4.3 2025.07.07';

// Debug logging
function debug(...args) {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}]`, ...args);
}

// 获取当前时间
function getCurrentTime() {
    return new Date().toLocaleTimeString();
}

// 样式已移至 content.css 文件

// 加载样式
function loadStyles() {
    // 样式已通过content.css文件加载，不需要在JS中创建样式元素
    debug('[样式] 样式已通过content.css加载');
}

// 页面元素选择器
const SELECTORS = {
    CHAT_INPUT: '.chat-message-input',  // 聊天输入框
    SEND_BUTTON: '.send-btn',  // 发送按钮
    CHAT_LIST: '.chat-list',  // 聊天列表
    UNREAD_COUNT: '.unread-count',  // 未读消息数量
    MESSAGE_ITEM: '.message-item',  // 消息项
    CHAT_CONTAINER: '.chat-container'  // 聊天容器
};

// 检查元素是否可见
function isElementVisible(el) {
    if (!el) return false;
    const style = window.getComputedStyle(el);
    return style.display !== 'none' &&
           style.visibility !== 'hidden' &&
           style.opacity !== '0' &&
           el.offsetWidth > 0 &&
           el.offsetHeight > 0;
}

// 获取元素的祖先节点信息
function getAncestorInfo(element, maxDepth = 5) {
    const ancestors = [];
    let current = element.parentElement;
    let depth = 0;
    
    while (current && depth < maxDepth) {
        ancestors.push({
            tagName: current.tagName,
            className: current.className,
            id: current.id,
            innerHTML: current.innerHTML.slice(0, 100), // 只取前100个字符
            computedStyle: {
                display: window.getComputedStyle(current).display,
                position: window.getComputedStyle(current).position
            }
        });
        current = current.parentElement;
        depth++;
    }
    return ancestors;
}

// 添加处理状态标志
let isProcessing = false;
// 创建Reply实例
const reply = new Reply();

// 初始化
function init() {
    console.log('[初始化] ' + getCurrentTime() + '脚本初始化init...');

    loadStyles();
    
    // 初始化关键词列表
    initializeKeywords();
    
    // 初始化自动同意修改地址为默认打开状态
    chrome.storage.local.get('modifyAddressEnabled').then(res => {
        if (res.modifyAddressEnabled === undefined) {
            chrome.storage.local.set({ modifyAddressEnabled: true });
            debug('[初始化] 设置自动同意修改地址默认为打开状态');
        }
    });


    // 设置回车发送功能
    reply.setupEnterToSend();
    


    // 初始化消息框处理器
    const messageHandler = window.messageBoxHandlerInstance;
    if (!messageHandler) {
        debug('[MessageBoxHandler] 错误: 未找到全局实例');
        return null;
    }
    messageHandler.startObserving();

    // 在页面卸载时停止监听
    window.addEventListener('unload', () => {
        messageHandler.stopObserving();
    });
}

// 向拼多多客户发送消息
async function sendToPddCustomer(message) {
    //读取配置是否要辅助人工回复
    let config = await StateManager.getState('aiSettings', {});
    const assistReplyEnabled = config?.assistReplyEnabled;
    const assistReplyTime = (config?.assistReplyTime > 0) ? config.assistReplyTime : 30;    //默认30秒
    if(assistReplyEnabled) {
        //倒计时发送
        return await reply.start(message, assistReplyTime);
    }else {
        //直接发送
        return await reply.start(message);
    }
}

// 存储每个会话的状态
const conversationStates = new Map();

// 转人工状态枚举
const TransferState = {
    IDLE: 'idle',           // 空闲状态
    TRANSFERRING: 'transferring',  // 正在转人工
    TRANSFERRED: 'transferred',    // 已转人工
    FAILED: 'failed'        // 转人工失败
};

// 转人工操作配置
const TRANSFER_CONFIG = {
    TIMEOUT: 30000,         // 转人工超时时间（30秒）
    MAX_ATTEMPTS: 2,        // 最大重试次数
    RETRY_DELAY: 5000      // 重试间隔（5秒）
};

// 转人工锁已不再使用

// 扩展会话状态类
class ConversationState {
    constructor(pddId, nickname, user_id='') {
        this.pddId = pddId;                    // 拼多多原始会话ID
        this.nickname = nickname;
        this.user_id = user_id;                // 用户ID，从data-random属性中提取
        this.lastProcessedTime = Date.now();
        this.processedMessages = new Set();
        this.transferState = TransferState.IDLE;
        this.lastTransferAttempt = null;
        this.transferAttempts = 0;
    }

    updateUser(nickname, user_id='') {
        this.nickname = nickname;
        this.user_id = user_id;
    }

    getUserName() {
        return this.nickname;
    }

    // 获取用于拼多多的会话ID
    getPddConversationId() {
        return this.pddId;
    }

    // 重置转人工状态
    resetTransferState() {
        this.transferState = TransferState.IDLE;
        this.lastTransferAttempt = null;
        this.transferAttempts = 0;
    }

    // 检查是否可以进行转人工操作
    canTransfer() {
        // 如果已经转人工或正在转人工，则不能重复操作
        if (this.transferState === TransferState.TRANSFERRED || 
            this.transferState === TransferState.TRANSFERRING) {
            return false;
        }

        // 如果之前失败且未超过最大重试次数，检查是否可以重试
        if (this.transferState === TransferState.FAILED) {
            if (this.transferAttempts >= TRANSFER_CONFIG.MAX_ATTEMPTS) {
                return false;
            }
            // 检查是否已经过了重试间隔时间
            if (this.lastTransferAttempt && 
                Date.now() - this.lastTransferAttempt < TRANSFER_CONFIG.RETRY_DELAY) {
                return false;
            }
        }

        return true;
    }
}

// 转人工功能已由handleTransfer函数实现

// 修改转人工相关函数调用
/**
 * @param {*} conversationId 
 * @returns 
 */
async function handleTransfer(conversationId) {
    debug('[转人工] 开始处理转人工请求:', conversationId);
    
    // 最大重试次数
    const MAX_RETRIES = 3;
    let retryCount = 0;
    
    // 获取设置中指定的客服账号列表
    const settings = await StateManager.getState('transferSettings', {});
    let serviceAccounts = settings.serviceAccounts || [];
    
    // 兼容旧版本，如果没有serviceAccounts但有specifiedAgent，则使用specifiedAgent
    if (serviceAccounts.length === 0 && settings.specifiedAgent) {
        serviceAccounts = [settings.specifiedAgent.trim()];
    }
    
    // 复制一份账号列表，用于随机选择并移除已尝试过的账号
    let availableAccounts = [...serviceAccounts];
    
    while (retryCount < MAX_RETRIES) {
        try {
            // 查找转移按钮
            const transferButton = await findTransferButton();
            if (!transferButton) {
                throw new Error('未找到转移按钮');
            }
            
            debug('[转人工] 找到转移按钮，准备点击');
            // 确保按钮在视图中
            transferButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await sleep(500);
            
            // 模拟真实点击
            const rect = transferButton.getBoundingClientRect();
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window,
                clientX: rect.left + rect.width / 2,
                clientY: rect.top + rect.height / 2
            });
            transferButton.dispatchEvent(clickEvent);
            await sleep(2000);
            
            // 选择目标客服
            // 修改：不再在selectTargetAgent中随机选择客服，而是在这里循环尝试所有可用的客服账号
            let targetAgent = null;
            let accountTrials = 0;
            
            // 尝试可用的客服账号，直到找到一个或者全部尝试失败
            while (!targetAgent && availableAccounts.length > 0 && accountTrials < availableAccounts.length) {
                // 随机选择一个客服账号
                const randomIndex = Math.floor(Math.random() * availableAccounts.length);
                const selectedAccount = availableAccounts[randomIndex];
                
                // 从可用账号列表中移除，避免重复尝试
                availableAccounts.splice(randomIndex, 1);
                
                debug('[转人工] 尝试查找客服账号:', selectedAccount, '(第', accountTrials + 1, '次尝试)');
                
                // 尝试查找这个客服账号
                targetAgent = await selectTargetAgent(selectedAccount);
                accountTrials++;
                
            if (!targetAgent) {
                    debug('[转人工] 未找到客服账号:', selectedAccount);
                }
            }
            
            if (!targetAgent) {
                // 本次重试所有账号都尝试完毕但未找到客服
                throw new Error('未找到目标客服');
            }
            
            debug('[转人工] 找到目标客服，准备点击');
            targetAgent.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await sleep(500);
            targetAgent.click();
            await sleep(2000);
            
            // 查找并点击转移原因按钮
            const confirmButton = await findTransferReasonButton();
            if (!confirmButton) {
                throw new Error('未找到转移原因按钮');
            }
            
                debug('[转人工] 找到转移原因按钮，准备点击');
            confirmButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await sleep(500);
            confirmButton.click();
            await sleep(2000);
            
            // 检查是否出现"取消收藏并转移"按钮或意见反馈对话框
            const cancelStarButton = await findCancelStarButton();
            if (cancelStarButton) {
                debug('[转人工] 找到后续操作按钮，准备点击:', cancelStarButton.textContent);
                cancelStarButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await sleep(500);
                    cancelStarButton.click();
                await sleep(2000);
            }
            
            // 更新状态为已转人工
            const state = conversationStates.get(conversationId);
            if (state) {
                state.transferState = TransferState.TRANSFERRED;
            }

            // 发送转人工成功提示
            await sendMessage('收到，请您稍候一下···');
            
            debug('[转人工] 转人工成功');
            
            // 不再刷新页面，直接返回成功
            return true;
            
        } catch (error) {
            debug('[转人工] 第', retryCount + 1, '次尝试失败:', error);
            retryCount++;
            
            // 如果还有账号可以尝试，继续尝试
            if (availableAccounts.length > 0) {
                debug('[转人工] 仍有', availableAccounts.length, '个客服账号可尝试');
            } else {
                // 重新填充账号列表以便下一次外层重试可以再次尝试所有账号
                availableAccounts = [...serviceAccounts];
                debug('[转人工] 所有客服账号都已尝试，将在下一次重试中重新尝试所有账号');
            }
            
            if (retryCount >= MAX_RETRIES) {
                debug('[转人工] 达到最大重试次数');
                // 更新状态为失败
                const state = conversationStates.get(conversationId);
                if (state) {
                    state.transferState = TransferState.FAILED;
                }
                
                // 自动关闭"自动转人工总开关"
                debug('[转人工] 所有客服账号尝试失败，自动关闭"自动转人工总开关"');
                const currentSettings = await StateManager.getState('transferSettings', {});
                
                // 更新设置，关闭自动转人工总开关
                await StateManager.setState('transferSettings', {
                    ...currentSettings,
                    manualEnabled: false
                });
                
                return await handleTransferFailed();
            }
            
            // 等待一段时间后重试
            await sleep(2000 * retryCount);
        }
    }
    
    return false;
}


let checkNotification_listenid = 0;
let checkUnreadMessages_listenid = 0;

// 统一的页面卸载清理管理器
const PageCleanupManager = {
    handlers: new Set(),
    initialized: false,
    
    // 添加清理处理器
    addHandler(handler) {
        this.handlers.add(handler);
        this.ensureInitialized();
    },
    
    // 移除清理处理器
    removeHandler(handler) {
        this.handlers.delete(handler);
    },
    
    // 确保初始化
    ensureInitialized() {
        if (this.initialized) return;
        
        const masterCleanupHandler = () => {
            debug('[清理] 页面卸载，执行所有清理处理器');
            for (const handler of this.handlers) {
                try {
                    handler();
                } catch (error) {
                    debug('[清理] 处理器执行失败:', error);
                }
            }
            this.handlers.clear();
        };
        
        window.addEventListener('beforeunload', masterCleanupHandler);
        window.addEventListener('unload', masterCleanupHandler);
        
        this.initialized = true;
    }
};

// 健康检查清理处理器
const healthCheckCleanup = () => {
    if (window.healthCheckTimer) {
        clearInterval(window.healthCheckTimer);
        window.healthCheckTimer = null;
        // 重置创建标志，确保下次可以重新创建
        window.healthCheckTimerCreating = false;
        debug('页面卸载时清理健康检查定时器');
    }
};

// 主要清理处理器
const mainCleanupHandler = () => {
    debug('页面卸载，开始清理资源');
    
    // 清理所有定时器
    if (window.statusMonitorTimer) {
        clearInterval(window.statusMonitorTimer);
        window.statusMonitorTimer = null;
    }
    
    if (checkUnreadMessages_listenid > 0) {
        clearInterval(checkUnreadMessages_listenid);
        checkUnreadMessages_listenid = 0;
    }
    
    if (checkNotification_listenid > 0) {
        clearInterval(checkNotification_listenid);
        checkNotification_listenid = 0;
    }
    
    // 清理健康检查定时器
    if (window.healthCheckTimer) {
        clearInterval(window.healthCheckTimer);
        window.healthCheckTimer = null;
    }
    window.healthCheckTimerCreating = false;

    // 重置所有状态
    isProcessing = false;
    jump_listenid_count = 0;
    window.lastProcessingStartTime = null;
    window.isProcessingMessage = false;
    window.lastMessageProcessingStartTime = null;
    window.hasRun = false;
    
    debug('资源清理完成');
};

// 注册清理处理器（替换原有的重复绑定）
PageCleanupManager.addHandler(healthCheckCleanup);
PageCleanupManager.addHandler(mainCleanupHandler);

let jump_listenid_count = 0;

// 监听拼多多消息
function listenToPDDMessages() {
    // 回复标记计数器
    let replyMarkCounter = 0;
    // 最大计数阈值，达到后刷新页面
    const MAX_COUNTER = 3;
    // 跟踪重复内容
    let lastMessageContent = '';
    let repeatContentCounter = 0;
    
    // 初始化回复消息存储
    initRepliedMessagesStorage();

    // 定期监控存储使用情况
    setInterval(monitorStorageUsage, 30 * 60 * 1000); // 每30分钟检查一次
    
    // 添加状态监控定时器，防止状态长时间锁死
    if (!window.statusMonitorTimer) {
        window.statusMonitorTimer = setInterval(() => {
            try {
                const currentTime = Date.now();
                
                // 检查页面是否还在拼多多商家后台
                const currentUrl = window.location.href;
                if (!currentUrl.includes('mms.pinduoduo.com/chat-merchant/index.html')) {
                    debug('页面已离开拼多多商家后台，清理状态监控定时器');
                    clearInterval(window.statusMonitorTimer);
                    window.statusMonitorTimer = null;
                    return;
                }
                
                // 检查 isProcessing 状态
                if (isProcessing) {
                    if (!window.lastProcessingStartTime) {
                        window.lastProcessingStartTime = currentTime;
                        debug('记录处理开始时间:', new Date(currentTime).toLocaleTimeString());
                    } else if (currentTime - window.lastProcessingStartTime > 30000) {
                        debug('检测到处理状态锁死超过30秒，强制重置');
                        debug('锁死详情 - 开始时间:', new Date(window.lastProcessingStartTime).toLocaleTimeString(), 
                            '当前时间:', new Date(currentTime).toLocaleTimeString(),
                            'jump_listenid_count:', jump_listenid_count);
                        
                        // 强制重置所有相关状态
                        isProcessing = false;
                        jump_listenid_count = 0;
                        window.lastProcessingStartTime = null;
                        window.isProcessingMessage = false;
                        
                        debug('强制重置完成，系统已恢复');
                    }
                } else {
                    // 正常状态，清理时间戳
                    if (window.lastProcessingStartTime) {
                        window.lastProcessingStartTime = null;
                    }
                }
                
                // 检查 window.isProcessingMessage 状态
                if (window.isProcessingMessage) {
                    if (!window.lastMessageProcessingStartTime) {
                        window.lastMessageProcessingStartTime = currentTime;
                    } else if (currentTime - window.lastMessageProcessingStartTime > 60000) {
                        debug('检测到消息处理状态锁死超过60秒，强制重置');
                        window.isProcessingMessage = false;
                        window.lastMessageProcessingStartTime = null;
                    }
                } else {
                    window.lastMessageProcessingStartTime = null;
                }
                
                // 定期输出状态信息（每分钟一次）
                if (!window.lastStatusLogTime || currentTime - window.lastStatusLogTime > 60000) {
                    debug('系统状态检查 - isProcessing:', isProcessing, 
                        'isProcessingMessage:', window.isProcessingMessage,
                        'jump_listenid_count:', jump_listenid_count);
                    window.lastStatusLogTime = currentTime;
                }
                
            } catch (error) {
                debug('状态监控器执行异常:', error);
                // 如果监控器本身出现异常，清理并重新创建
                if (error.name === 'TypeError' || error.message.includes('Cannot read property')) {
                    debug('状态监控器异常严重，重新创建');
                    clearInterval(window.statusMonitorTimer);
                    window.statusMonitorTimer = null;
                    // 延迟重新创建，避免立即再次出错
                    setTimeout(() => {
                        if (!window.statusMonitorTimer) {
                            debug('重新创建状态监控定时器');
                            // 这里会重新调用当前函数的逻辑
                        }
                    }, 5000);
                }
            }
        }, 5000); // 每5秒检查一次
        
        debug('状态监控定时器已启动');
        
        // 添加页面卸载事件监听，确保定时器被清理
        const statusMonitorCleanup = () => {
            if (window.statusMonitorTimer) {
                clearInterval(window.statusMonitorTimer);
                window.statusMonitorTimer = null;
                debug('页面卸载时清理状态监控定时器');
            }
        };
        
        // 监听多种页面卸载事件
        window.addEventListener('beforeunload', statusMonitorCleanup);
        window.addEventListener('unload', statusMonitorCleanup);
        window.addEventListener('pagehide', statusMonitorCleanup);
        
        // 存储清理函数引用，便于后续移除
        window.statusMonitorCleanupHandler = statusMonitorCleanup;
    }



    debug('开始在监听拼多多消息...');
    
    // 检查是否在拼多多商家后台页面
    const currentUrl = window.location.href;
    debug('Current URL:', currentUrl);
    
    if (!currentUrl.includes('mms.pinduoduo.com/chat-merchant/index.html')) {
        debug('未检测到拼多多商家后台页面');
        return;
    }

    debug('成功连接到拼多多商家后台');

    // 获取客户名称
    function getCustomerName() {
        const nameElement = document.evaluate(
            '/html/body/div[1]/div/div[1]/div[3]/div[1]/div[5]/div[1]/div[1]/div[1]/span[1]',
            document,
            null,
            XPathResult.FIRST_ORDERED_NODE_TYPE,
            null
        ).singleNodeValue;

        const nameEndingElement = document.evaluate(
            '/html/body/div[1]/div/div[1]/div[2]/div[1]/span[2]',
            document,
            null,
            XPathResult.FIRST_ORDERED_NODE_TYPE,
            null
        ).singleNodeValue;

        return `${nameElement?.textContent || ''}${nameEndingElement?.textContent || ''}`;
    }

    // 获取商品名称
    function getProductName() {
        const productElement = document.evaluate(
            '((//ul/li/div[@class="cs-item isread" or @class="cs-item unread"])[last()]/ancestor::li[@class="clearfix onemsg"]/following-sibling::li/div[@class="notify-card"]//p)[last()]',
            document,
            null,
            XPathResult.FIRST_ORDERED_NODE_TYPE,
            null
        ).singleNodeValue;

        return productElement?.textContent || '';
    }

    // 切换到指定对话
    async function switchToChat(chatItem) {
        try {
            const chatItemBox = chatItem.querySelector('.chat-item-box');
            if (!chatItemBox) {
                debug('未找到chat-item-box元素');
                return false;
            }

            // 创建并触发鼠标事件
            const mousedownEvent = new MouseEvent('mousedown', {
                bubbles: true,
                cancelable: true,
                view: window
            });

            const mouseupEvent = new MouseEvent('mouseup', {
                bubbles: true,
                cancelable: true,
                view: window
            });

            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });

            // 按顺序触发事件
            chatItemBox.dispatchEvent(mousedownEvent);
            await new Promise(resolve => setTimeout(resolve, 50));
            chatItemBox.dispatchEvent(mouseupEvent);
            await new Promise(resolve => setTimeout(resolve, 50));
            chatItemBox.dispatchEvent(clickEvent);

            // 等待对话加载
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 验证是否切换成功
            return chatItemBox.classList.contains('active');
        } catch (error) {
            debug('切换对话失败:', error);
            return false;
        }
    }

    // 检查并处理未读消息列表
    async function checkUnreadMessages() {
        //检查是否有弹窗,有的话点掉
        checkpopupreplay();
        
        // 检查是否有"已等待X分钟"的提示，如有进行处理
        if (checkWaitingTime()) {
            debug('[监听消息]检测到等待时间异常，已处理');
            return;
        }
        
        debug('[监听消息]开始检查未读消息');
        if (isProcessing) {
            if(jump_listenid_count > 20){
                // 强制重置处理状态，避免死锁
                debug('检测到可能的死锁，强制重置处理状态');
                isProcessing = false;
                jump_listenid_count = 0;
                // 继续执行，不return
            }else {
                jump_listenid_count++;
                debug('消息处理中，跳过新的检查，计数:', jump_listenid_count);
                return;
            }
        }

        //检查是否有输入中的信息
        let reply_content = document.querySelector('#replyTextarea')?.value;
        if(reply_content) {
            debug('有输入中的信息，跳过检查未读消息');
            return;
        }

        try {
            // 重置计数器，开始新的处理周期
            jump_listenid_count = 0;
            isProcessing = true;
            window.lastProcessingStartTime = Date.now(); // 记录开始处理的时间


                    // 查找所有带有未读标记的聊天项
        const unreadItems = document.evaluate(
            '//div[@class="chat-portrait"]/i[not(@style="display: none;")]/ancestor::li[@class="chat-item"]',
            document,
            null,
            XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
            null
        );
        
        debug('[监听消息]发现未读消息数量:', unreadItems.snapshotLength);
        
        // 发现未读消息后，主动检查是否有转移会话弹窗
        if (unreadItems.snapshotLength > 0) {
            debug('[未读消息] 检测到未读消息，主动检查转移会话弹窗');
            checkExistingTransferDialogs();
        }

        // 检查是否有"下单数"的买家，如果有，优先处理
        if (unreadItems.snapshotLength > 1) {
            debug('[下单数检测] 开始检查是否有下单数买家');
            let hasOrderCountBuyer = false;
            let orderCountChatItem = null;
            
            for (let i = 0; i < unreadItems.snapshotLength; i++) {
                const chatItem = unreadItems.snapshotItem(i);
                // 查找是否有"下单数"标签
                const orderCountTag = chatItem.querySelector('.chat-tag');
                if (orderCountTag && orderCountTag.textContent.includes('下单数')) {
                    debug('[下单数检测] 发现下单数买家');
                    hasOrderCountBuyer = true;
                    orderCountChatItem = chatItem;
                    break;
                }
            }
            
            // 如果找到带有"下单数"标签的买家，优先处理这个
            if (hasOrderCountBuyer && orderCountChatItem) {
                debug('[下单数检测] 开始处理下单数买家');
                
                // 获取会话ID和昵称
                const pddConversationId = getConversationId(orderCountChatItem);
                
                // 检查会话ID是否有效
                if (!pddConversationId) {
                    debug('[下单数检测] 无效的会话ID，跳过处理');
                    return;
                }
                
                // 获取原始完整的data-random属性值
                const chatItemBox = orderCountChatItem.querySelector('.chat-item-box');
                const fullDataRandom = chatItemBox?.getAttribute('data-random') || '';
                
                // 获取昵称和用户ID
                let nickname = orderCountChatItem.querySelector('.nickname-span')?.textContent || '';
                let user_id = extractNumericUserId(pddConversationId) || '';
                debug('[下单数检测] 从data-random属性提取到用户ID:', user_id);
                
                debug('[下单数检测] 准备处理下单数买家对话:', nickname, 'pddConversationId:', pddConversationId);
                
                // 获取或创建会话状态
                let cs;
                if (!conversationStates.has(pddConversationId)) {
                    cs = new ConversationState(pddConversationId, nickname, user_id)
                    conversationStates.set(pddConversationId, cs);
                    debug('创建新会话状态');
                } else {
                    debug('[下单数检测] 更新昵称和用户ID', nickname, user_id);
                    cs = conversationStates.get(pddConversationId)
                    if(cs) {
                        cs.updateUser(nickname, user_id)
                        conversationStates.set(pddConversationId, cs)
                        debug('[下单数检测] 更新昵称和用户ID成功', nickname, user_id);
                    }
                }

                // 切换到该对话
                const switchSuccess = await switchToChat(orderCountChatItem);
                if (!switchSuccess) {
                    debug('[下单数检测] 切换对话失败');
                    return;
                }

                debug('[下单数检测] 切换对话成功，等待1秒');
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 强制比对昵称 
                let chat_name = document.querySelector('.base-info span.name')?.textContent;
                if (!chat_name || chat_name !== nickname) {
                    debug('[下单数检测] 不是同一个用户', chat_name, nickname)
                    return;
                }
                
                // 提取订单信息
                debug('[下单数检测] 开始提取订单信息');
                try {
                    // 首先尝试通过findBackupKeywordOrderItem函数获取订单项
                    debug('[下单数检测] 尝试通过点击最新订单获取订单信息');
                    const orderItem = await findBackupKeywordOrderItem();

                    let orderStatus = '';
                    let orderNumber = '';
                    let orderTime = '';
                    let goodsPrice = '';
                    let goodsNum = '';
                    let remarkNote = '';
                    let orderCount = '';
                    let goodsName = '';
                    let amountValue = '';

                    if (orderItem) {
                        debug('[下单数检测] 成功获取到订单项，开始提取详细信息');

                        // 从订单项中提取信息
                        orderStatus = orderItem.querySelector('.order-status .title-status')?.textContent.trim() || '';
                        orderNumber = orderItem.querySelector('.order-number .order-sn')?.textContent.trim() || '';
                        orderTime = orderItem.querySelector('.order-confirm span:last-child')?.textContent.trim() || '';
                        goodsPrice = orderItem.querySelector('.goods-price')?.textContent.trim() || '';
                        goodsNum = orderItem.querySelector('.goods-num')?.textContent.replace('x', '').trim() || '';
                        remarkNote = orderItem.querySelector('.remark-note')?.textContent.trim() || '';
                        goodsName = orderItem.querySelector('.goods-name')?.textContent.trim() || '';
                        amountValue = orderItem.querySelector('.amount-value')?.textContent.trim() || '';

                        // 使用商品数量作为下单数
                        if (goodsNum) {
                            orderCount = goodsNum;
                            debug('[下单数检测] 从订单项提取到下单数量:', orderCount);
                        }

                        debug('[下单数检测] 从订单项提取的信息:', {
                            orderStatus, orderNumber, orderTime, goodsPrice, goodsNum, remarkNote, orderCount, goodsName, amountValue
                        });

                        // 详细显示每个字段的值
                        debug('[下单数检测] 详细信息:');
                        debug('  - 订单状态:', orderStatus);
                        debug('  - 订单编号:', orderNumber);
                        debug('  - 下单时间:', orderTime);
                        debug('  - 商品价格:', goodsPrice);
                        debug('  - 商品数量:', goodsNum);
                        debug('  - 订单备注:', remarkNote);
                        debug('  - 下单数量:', orderCount);
                        debug('  - 商品名称:', goodsName);
                        debug('  - 实付金额:', amountValue);
                    } else {
                        debug('[下单数检测] 未能获取到订单项，尝试从当前页面直接提取');

                        // 回退方案：直接从当前页面提取
                        orderStatus = document.querySelector('.order-status .title-status')?.textContent.trim() || '';
                        orderNumber = document.querySelector('.order-number .order-sn')?.textContent.trim() || '';
                        orderTime = document.querySelector('.order-confirm span:last-child')?.textContent.trim() || '';
                        goodsPrice = document.querySelector('.goods-price')?.textContent.trim() || '';
                        goodsNum = document.querySelector('.goods-num')?.textContent.replace('x', '').trim() || '';
                        remarkNote = document.querySelector('.remark-note')?.textContent.trim() || '';
                        goodsName = document.querySelector('.goods-name')?.textContent.trim() || '';
                        amountValue = document.querySelector('.amount-value')?.textContent.trim() || '';

                        // 尝试从右侧面板中的订单信息提取数据
                        const orderItems = document.querySelectorAll('.order-item, .order-item-list .order-item');
                        if (orderItems && orderItems.length > 0) {
                            debug('[下单数检测] 找到' + orderItems.length + '个订单项，提取订单信息');

                            const firstOrderItem = orderItems[0];
                            const itemGoodsNum = firstOrderItem.querySelector('.goods-num')?.textContent.replace('x', '').trim() || '';
                            if (itemGoodsNum) {
                                orderCount = itemGoodsNum;
                                debug('[下单数检测] 从订单项提取到下单数量:', orderCount);
                            }
                        }

                        // 如果还是没有获取到下单数，尝试从聊天标签获取
                        if (!orderCount) {
                            debug('[下单数检测] 未找到订单项，尝试从聊天标签获取');
                            const chatTags = document.querySelectorAll('.chat-tag');
                            for (const tag of chatTags) {
                                const match = tag.textContent.match(/下单数\((\d+)\)/);
                                if (match) {
                                    orderCount = match[1];
                                    debug('[下单数检测] 从聊天标签提取到下单数:', orderCount);
                                    break;
                                }
                            }
                        }
                    }
                    
                    const orderInfo = {
                        orderStatus,
                        orderNumber,
                        orderTime,
                        goodsPrice,
                        goodsNum,
                        remarkNote,
                        orderCount,
                        goodsName,
                        amountValue
                    };
                    
                    debug('[下单数检测] 提取到的订单信息:', orderInfo);
                    
                    // 获取最新消息
                    const lastMessage = await getLatestMessage(pddConversationId);
                    debug('[下单数检测] 获取到最新消息:', lastMessage);
                    if (lastMessage) {
                        // 调用DIFY API处理订单信息并生成回复
                        let formattedUserInfo = getTopUsername();
                        debug('[下单数检测] 发送给DIFY的用户标识:', formattedUserInfo);
                        
                        // 先定义sendDifyOrderInfoRequest函数，如果还没定义
                        if (typeof sendDifyOrderInfoRequest !== 'function') {
                            /**
                             * 发送订单信息到DIFY进行处理
                             * @param {Object} orderInfo - 订单信息对象
                             * @param {string} userInfo - 用户标识信息
                             * @param {string} conversationId - 会话ID
                             * @param {Object} state - 会话状态对象
                             * @param {Object} message - 消息对象
                             * @returns {Promise<boolean>} - 处理成功返回true，否则返回false
                             */
                            window.sendDifyOrderInfoRequest = async function(orderInfo, userInfo, conversationId, state, message) {
                                try {
                                    // 使用已定义的formatOrderInfo函数
                                    // 使用公共的formatOrderInfo函数
                                    const { formatOrderInfo } = await import('../utils/common.js');
                                    
                                            // 将订单信息格式化为文本
        // 使用已导入的formatOrderInfo函数
        
        const formattedOrderInfo = formatOrderInfo(orderInfo);
        debug('[下单数处理] 格式化后的订单信息:', formattedOrderInfo);

                                    // 添加上下文信息
                                    const requestContent = `这是一个下单数买家的订单信息:\n${formattedOrderInfo}\n请为这个客户提供一个适合的回复。`;
                                    
                                    // 发送请求到DIFY API
                                    const response = await difyAPI.sendMessage(requestContent, false, {
                                        userIdentifier: userInfo
                                    });
                                    
                                    // 处理DIFY API的响应
                                    if (response && response.answer) {
                                        const difyAnswer = response.answer.trim();
                                        debug('[下单数处理] DIFY回复:', difyAnswer);
                                        
                                        // 发送消息
                                        if (difyAnswer) {
                                            const success = await sendToPddCustomer(difyAnswer);
                                            
                                            // 标记消息为已回复
                                            if (success) {
                                                if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                                                    for (const msgElement of message.unrepliedMessages) {
                                                        await markMessageAsReplied(msgElement, conversationId);
                                                    }
                                                } else if (message.element) {
                                                    await markMessageAsReplied(message.element, conversationId);
                                                }
                                                
                                                // 更新会话状态
                                                if (state) {
                                                    state.processedMessages.add(message.id);
                                                    state.lastProcessedTime = Date.now();
                                                }
                                                
                                                // 显示成功状态
                                                floatingBallInstance?.setStatus('success');
                                                return true;
                                            }
                                        }
                                    }
                                    
                                    return false;
                                } catch (error) {
                                    debug('[下单数处理] 处理订单信息失败:', error);
                                    floatingBallInstance?.setStatus('error');
                                    return false;
                                }
                            };
                        }
                        
                        // 调用函数处理订单信息
                        const success = await sendDifyOrderInfoRequest(orderInfo, formattedUserInfo, pddConversationId, cs, lastMessage);
                        if (!success) {
                            // 如果处理失败，回退到标准消息处理
                            debug('[下单数检测] DIFY处理失败，回退到标准消息处理');
                            await processMessage(lastMessage, pddConversationId);
                        }
                    }
                    
                    // 处理完成后直接返回，不继续处理其他未读消息
                    return;
                } catch (error) {
                    debug('[下单数检测] 提取订单信息失败:', error);
                }
            }
        }

        // 修改：只处理第一个未读消息，而不是循环处理所有
            if (unreadItems.snapshotLength > 0) {
                debug('[监听消息]开始处理第一个未读消息');
                const chatItem = unreadItems.snapshotItem(0);
                if (chatItem) {
                    // 获取会话ID和昵称
                    const pddConversationId = getConversationId(chatItem);
                    
                    // 检查会话ID是否有效，不再检查是否包含'-unTimeout'
                    if (!pddConversationId) {
                        debug('[监听消息]无效的会话ID，跳过处理');
                        return;
                    }
                    
                    // 获取原始完整的data-random属性值，记录信息但不用于过滤
                    const chatItemBox = chatItem.querySelector('.chat-item-box');
                    const fullDataRandom = chatItemBox?.getAttribute('data-random') || '';
                    
                    // 获取昵称和用户ID - 修复未定义变量错误
                    let nickname = chatItem.querySelector('.nickname-span')?.textContent || '';
                    // 使用data-random属性中的数字ID作为用户标识
                    let user_id = extractNumericUserId(pddConversationId) || '';
                    debug('从data-random属性提取到用户ID:', user_id);
                    
                    debug('[监听消息]准备处理未读对话:', nickname, 'pddConversationId:', pddConversationId);
                    
                    // 获取或创建会话状态
                    let cs;
                    if (!conversationStates.has(pddConversationId)) {
                        cs = new ConversationState(pddConversationId, nickname, user_id)
                        conversationStates.set(pddConversationId, cs);
                        debug('创建新会话状态');
                    }else {
                        debug('[游客debug]更新昵称和用户ID',nickname, user_id);
                        cs = conversationStates.get(pddConversationId)
                        if(cs) {
                            cs.updateUser(nickname, user_id)
                            conversationStates.set(pddConversationId, cs)
                            debug('[游客debug]更新昵称和用户ID成功',nickname, user_id);
                        }
                    }

                    // 切换到该对话
                    const switchSuccess = await switchToChat(chatItem);
                    if (!switchSuccess) {
                        debug('[监听消息]切换对话失败');
                        return;
                    }

                    debug('切换对话成功');
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    //强制比对昵称 
                    let chat_name = document.querySelector('.base-info span.name')?.textContent;
                    if(!chat_name || chat_name !==nickname ) {
                        debug('不是同一个用户', chat_name, nickname)
                        return;
                    }

                    // 获取并处理最新消息
                    const lastMessage = await getLatestMessage(pddConversationId);
                    debug('[监听消息]获取到最新消息:', lastMessage);
                    if (lastMessage) {

                        //重新获取下昵称
                        let new_nickname = await checkUsername(nickname);
                        if(new_nickname === 'youke'){
                            //切换会话
                            return;
                        }
                        if(new_nickname !== nickname || cs.getUserName() == '游客'){
                            nickname = new_nickname;
                            // 使用之前提取的用户ID作为标识
                            debug('使用用户ID作为标识', user_id);
                            conversationStates.set(pddConversationId, new ConversationState(pddConversationId, new_nickname, user_id));
                        }

                        //判断是否是表情
                        let isEmoji = false;
                        let bottom_message = chatItem.querySelector('.bottom-message .chat-message-content');
                        if(bottom_message){
                            let emoji_content = bottom_message.textContent;
                            if(/^\[.*\]$/.test(emoji_content) && !emoji_content.includes('商品') && !emoji_content.includes('买家申请修改为新地址') ){
                                //多媒体独立处理
                                if(emoji_content == '[图片]' || emoji_content == '[视频]' || emoji_content.length > 5){
                                    if(await checkDifyMediaEnabled()) {
                                        debug('【匹配】转多媒体给dify', emoji_content);
                                        let top_username = getTopUsername();
                                        if(top_username === 'youke') {
                                            debug('【游客】跳过游客多媒体消息');
                                            return;
                                        }
                                        // 使用格式化的用户标识（客服账号+昵称+用户ID）
                                        const formattedUserInfo = getTopUsername();
                                        debug('发送给DIFY的用户标识:', formattedUserInfo);
                                        return await sendDifyMediaRequest(emoji_content+'消息', formattedUserInfo);


                                    }
                                }else {
                                    //纯表情
                                    debug('【表情】收到表情消息',emoji_content);
                                    sendMessage( getEmojiByType('product_card'));
                                    return;
                                }
                            }
                        }

                        
                        await processMessage(lastMessage, pddConversationId);
                    }
                }
            } else {
                debug('[监听消息]没有发现未读消息');
            }
        } catch (error) {
            debug('未读消息处理异常:', error);
            // 记录异常详情，便于调试
            debug('异常发生时的状态 - isProcessing:', isProcessing, 'jump_listenid_count:', jump_listenid_count);
            
            // 异常时重置计数器，避免影响下次处理
            jump_listenid_count = 0;
            
            // 如果是严重异常，可以考虑暂停一段时间
            if (error.name === 'TypeError' || error.message.includes('Cannot read property')) {
                debug('检测到严重异常，延迟下次检查');
                setTimeout(() => {
                    debug('异常恢复，重新开始检查');
                }, 5000);
            }
        } finally {
            isProcessing = false;
            debug('checkUnreadMessages 处理完成，重置状态');
        }
    }

    // 检查消息是否已回复
    async function hasReplyMark(messageElement, conversationId) {
        if (!messageElement) return false;
        
        // 1. 首先检查DOM中是否有标记
        const hasDomMark = !!(
            messageElement.querySelector('.replied-mark') || 
            messageElement.querySelector('.msg-content-box .replied-mark') ||
            messageElement.querySelector('.text-content .replied-mark')
        );
        
        if (hasDomMark) return true;
        
        // 2. 检查消息内容中是否包含"✓"符号（已回复标记）
        const messageContent = messageElement.textContent || '';
        if (messageContent.includes('✓')) {
            debug('检测到消息内容中包含"✓"符号，视为已回复');
            return true;
        }
        
        // 3. 如果DOM中没有标记，检查存储中是否有记录
        if (conversationId) {
            try {
                // 直接尝试使用元素ID，避免generateMessageId的作用域问题
                const messageId = messageElement.id || (Date.now() + '_' + Math.random().toString(36).substring(2, 8));
                return await isMessageReplied(conversationId, messageId);
            } catch (error) {
                debug('检查消息是否已回复时出错:', error);
                return false;
            }
        }
        
        return false;
    }

    // 获取当前会话的最后一条买家消息
    async function getLatestMessage(conversationId) {
        try {
            // 获取所有买家消息
            const allMessageElements = document.evaluate(
                '//li[contains(@class, "clearfix") and contains(@class, "onemsg") and .//div[ (contains(@class, "buyer") or contains(@class, "lego-card") or contains(@class, "notify-card") ) and not(contains(@class, "system-msg")) ]]',
                document,
                null,
                XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
                null
            );
            
            if (allMessageElements.snapshotLength === 0) {
                debug('未找到买家消息元素');
                return null;
            }
            
            // 从最后一条消息开始，向前查找未标记为已回复的消息
            let combinedTextContent = '';  // 合并所有文本类消息内容
            let lastUnrepliedMessage = null;
            let hasUnrepliedMessages = false;
            let unrepliedMessages = []; // 存储所有未回复的消息元素
            let productMessages = [];  // 存储商品卡消息
            let textMessages = [];    // 存储文本类消息
            let otherMessages = [];   // 存储其他类型消息
            
            // 从最后一条消息开始，向前查找
            for (let i = allMessageElements.snapshotLength - 1; i >= 0; i--) {
                const messageElement = allMessageElements.snapshotItem(i);
                
                // 首先通过ID判断消息是否已经处理过
                const messageId = messageElement.id || generateMessageId(messageElement);
                const isAlreadyReplied = await isMessageReplied(conversationId, messageId);
                
                // 检查消息是否已回复（通过DOM标记或存储记录）
                if (isAlreadyReplied || await hasReplyMark(messageElement, conversationId)) {
                    // 如果找到已回复的消息，停止向前查找
                    if (hasUnrepliedMessages) {
                        break;
                    }
                    continue;
                }
                
                // 找到未回复的消息
                hasUnrepliedMessages = true;
                unrepliedMessages.push(messageElement); // 添加到未回复消息列表
                
                // 先检查消息类型
                // 检查是否是商品卡
                const productInfo = getProductCardInfo(messageElement);
                if (productInfo) {
                    // 再次确认这个商品卡消息是否已被处理过
                    const productMessageId = messageElement.id || generateMessageId(messageElement);
                    const productAlreadyReplied = await isMessageReplied(conversationId, productMessageId);
                    
                    if (!productAlreadyReplied) {
                        // 只有确认未处理过的商品卡才添加到列表
                        debug('找到未处理的商品卡消息，ID:', productMessageId);
                    productMessages.push(messageElement);
                    } else {
                        debug('发现已处理的商品卡消息，跳过，ID:', productMessageId);
                        // 从未回复消息列表中移除
                        unrepliedMessages.pop();
                        continue;
                    }
                } else {
                    // 提取消息内容
                let messageContent = extractMessageContent(messageElement);
                    
                    // 判断其他类型
                    if (messageContent && messageContent.trim() && !messageContent.startsWith('[图片]')) {
                        // 文本消息
                        textMessages.push(messageElement);
                        if (combinedTextContent) {
                            combinedTextContent = messageContent + '\n' + combinedTextContent;
                        } else {
                            combinedTextContent = messageContent;
                        }
                    } else if (messageElement.querySelector('.lego-card, .order-card, .refund-card, .apply-card') || 
                              messageElement.querySelector('img, .image-msg, .video-content') || 
                              messageContent === '[图片]') {
                        // 其他类型消息（卡片、图片等）
                        otherMessages.push(messageElement);
                    } else {
                        // 未识别类型，当作文本处理
                        textMessages.push(messageElement);
                if (messageContent && messageContent.trim()) {
                            if (combinedTextContent) {
                                combinedTextContent = messageContent + '\n' + combinedTextContent;
                    } else {
                                combinedTextContent = messageContent;
                            }
                        }
                    }
                }
                
                // 记录最后一条未回复的消息元素（时间最新的）
                if (!lastUnrepliedMessage) {
                    lastUnrepliedMessage = messageElement;
                }
            }
            
            // 如果没有找到未回复的消息，返回最后一条消息（保持原有逻辑）
            if (!lastUnrepliedMessage) {
                // ... 原有代码保持不变 ...
                const lastMessageElement = allMessageElements.snapshotItem(allMessageElements.snapshotLength - 1);
                
                // 先检查是否是商品卡
                const productInfo = getProductCardInfo(lastMessageElement);
                debug('获取商品信息:', productInfo);
                if (productInfo) {
                    //详情卡需要特殊处理，判断上一条文字
                    let productContent = '';
                    let p_type = MESSAGE_TYPES.PRODUCT;

                    if(productInfo.type === 1){
                        let card_content = await checkTextAndProduct(lastMessageElement);
                        if(card_content !== false){
                            productContent = `咨询商品ID：${productInfo.goodsId}，商品名：${productInfo.goodsName}，提问：${card_content} `;
                        }else{
                            productContent = `咨询商品ID：${productInfo.goodsId}，商品名：${productInfo.goodsName}`;
                        }
                    }else if(productInfo.type === 3){
                        productContent = `商品名：${productInfo.goodsName}，规格：${productInfo.skuName}`;
                    }else if(productInfo.type === 2){
                        let card_content = await checkTextAndProduct(lastMessageElement);
                        if(card_content !== false){
                            productContent = `商品名：${productInfo.goodsName} ，提问：${card_content} `;
                        }else{
                            productContent = `商品名：${productInfo.goodsName} `;
                        }
                    }

                                    return {
                    element: lastMessageElement,
                    type: p_type,
                    content: productContent,
                    id: generateMessageId(lastMessageElement),
                    productInfo: productInfo,
                    unrepliedMessages: unrepliedMessages // 添加未回复消息列表
                };
                }

                // 再获取其他类型消息内容
                let messageContent = extractMessageContent(lastMessageElement);
                debug('提取到消息内容:', messageContent);
                
                // 判断其他类型消息
                let messageType = 'unknown';
                
                if (messageContent && messageContent.trim() && !messageContent.startsWith('[图片]')) {
                    messageType = 'text';
                    //判断卡片和文字结合体
                    let pat = checkProductAndText(lastMessageElement);
                    if(pat) {
                        messageType = 'ProductAndText';

                        //根据场景结果，拼接content
                        let productContent = '';
                        if(pat.type === 3){
                            //下方文案
                            productContent = `商品名：${pat.goodsName}，规格：${pat.skuName}`;
                        }

                        messageContent = productContent+`\n提问：${messageContent}`;
                    }
                } else if (lastMessageElement.querySelector('.lego-card')) {
                    messageType = 'legocard';   //卡片，修改地址卡片
                    if(lastMessageElement.querySelector('.lego-card .title')?.textContent?.includes('售后')) {//售后卡片
                        messageType = 'refund';
                    }
                } else if (lastMessageElement.querySelector('.order-card')) {
                    messageType = 'order';
                } else if (lastMessageElement.querySelector('.refund-card, .apply-card')) {
                    messageType = 'refund';
                } else if (lastMessageElement.querySelector('img, .image-msg, .video-content') || messageContent === '[图片]') {
                    messageType = 'image';
                } else if(lastMessageElement.querySelector('.notify-card .title')?.textContent?.includes('售后')) {
                    if(lastMessageElement.querySelector('.notify-card .title')?.textContent != '请尽快处理售后，避免平台介入') {
                        messageType = 'refund';
                    }
                }

                debug(`消息类型判断结果: ${messageType}, 内容: ${messageContent}`);

                return {
                    element: lastMessageElement,
                    type: messageType,
                    content: messageContent,
                    id: generateMessageId(lastMessageElement)
                };
            }
            
            // 有未回复的消息，需要处理
            debug(`找到 ${unrepliedMessages.length} 条未回复消息，其中商品卡消息 ${productMessages.length} 条，文本消息 ${textMessages.length} 条，其他消息 ${otherMessages.length} 条`);
            
            // 优先处理商品卡消息
            if (productMessages.length > 0) {
                // 使用最新的商品卡消息（数组中第一个元素）
                const productElement = productMessages[0];
                const productInfo = getProductCardInfo(productElement);
                debug('找到商品卡消息，获取商品信息:', productInfo);
                
            if (productInfo) {
                //详情卡需要特殊处理，判断上一条文字
                let productContent = '';
                let p_type = MESSAGE_TYPES.PRODUCT;

                if(productInfo.type === 1){
                        let card_content = await checkTextAndProduct(productElement);
                    if(card_content !== false){
                        productContent = `咨询商品ID：${productInfo.goodsId}，商品名：${productInfo.goodsName}，提问：${card_content} `;
                    }else{
                        productContent = `咨询商品ID：${productInfo.goodsId}，商品名：${productInfo.goodsName}`;
                    }
                }else if(productInfo.type === 3){
                    productContent = `商品名：${productInfo.goodsName}，规格：${productInfo.skuName}`;
                }else if(productInfo.type === 2){
                        let card_content = await checkTextAndProduct(productElement);
                    if(card_content !== false){
                        productContent = `商品名：${productInfo.goodsName} ，提问：${card_content} `;
                    }else{
                        productContent = `商品名：${productInfo.goodsName} `;
                    }
                }

                return {
                        element: productElement,
                    type: p_type,
                    content: productContent,
                        id: generateMessageId(productElement),
                    productInfo: productInfo,
                        unrepliedMessages: unrepliedMessages, // 所有未回复消息
                        productMessages: productMessages,     // 商品卡消息
                        textMessages: textMessages,           // 文本消息
                        otherMessages: otherMessages          // 其他消息
                    };
                }
            }
            
            // 如果没有商品卡消息或商品卡信息提取失败，则处理文本消息
            if (textMessages.length > 0) {
                // 使用最新的文本消息（数组中第一个元素）
                const textElement = textMessages[0];
                
                // 判断是否有卡片和文字结合体
                let messageType = 'text';
                let pat = checkProductAndText(textElement);
                if (pat) {
                    messageType = 'ProductAndText';

                    //根据场景结果，拼接content
                    let productContent = '';
                    if (pat.type === 3) {
                        //下方文案
                        productContent = `商品名：${pat.goodsName}，规格：${pat.skuName}`;
                    }

                    combinedTextContent = productContent + `\n提问：${combinedTextContent}`;
                }

                return {
                    element: textElement,
                    type: messageType,
                    content: combinedTextContent,
                    id: generateMessageId(textElement),
                    unrepliedMessages: unrepliedMessages, // 所有未回复消息
                    productMessages: productMessages,     // 商品卡消息
                    textMessages: textMessages,           // 文本消息
                    otherMessages: otherMessages          // 其他消息
                };
            }
            
            // 如果既没有商品卡也没有文本消息，使用最后一条未回复的消息
            const messageType = lastUnrepliedMessage.querySelector('.lego-card') ? 'legocard' : 
                              lastUnrepliedMessage.querySelector('.order-card') ? 'order' : 
                              lastUnrepliedMessage.querySelector('.refund-card, .apply-card') ? 'refund' : 
                              lastUnrepliedMessage.querySelector('img, .image-msg, .video-content') ? 'image' : 'unknown';

            return {
                element: lastUnrepliedMessage,
                type: messageType,
                content: extractMessageContent(lastUnrepliedMessage),
                id: generateMessageId(lastUnrepliedMessage),
                unrepliedMessages: unrepliedMessages, // 所有未回复消息
                productMessages: productMessages,     // 商品卡消息
                textMessages: textMessages,           // 文本消息
                otherMessages: otherMessages          // 其他消息
            };
            
        } catch (error) {
            debug('获取最新消息时出错:', error);
            return null;
        }
    }

    // 提取消息内容的函数
    function extractMessageContent(element) {
        if (!element) return '';

        let content = '';

        // 获取文本内容
        const textElement = element.querySelector('.msg-content-box, .text-content');
        if (textElement) {
            content = textElement.textContent?.trim() || '';
            debug('提取到文本内容:', content);
        }

        // 检查商品卡
        const goodCard = element.querySelector('.msg-content.good-card');
        if (goodCard) {
            const productName = goodCard.querySelector('.good-name')?.textContent?.trim();
            const productPrice = goodCard.querySelector('.good-price')?.textContent?.trim();
            const productId = goodCard.querySelector('.good-id')?.textContent?.replace('商品ID：', '').replace('复制', '')?.trim();
            
            // 将商品信息作为文本内容
            content = `商品咨询：${productName}\n价格：${productPrice}\n商品ID：${productId}`;
            debug('提取到商品卡内容:', content);
        }

        // 检查 notify-card
        const notifyCard = element.querySelector('.notify-card');
        if (notifyCard) {
            const productName = notifyCard.querySelector('p')?.textContent?.trim();
            const productPrice = notifyCard.querySelector('span:last-child')?.textContent?.trim();
            const source = notifyCard.querySelector('.title span')?.textContent?.trim();
            
            if (content) {
                content += `\n\n[用户${source}]\n商品：${productName}\n价格：${productPrice}`;
            } else {
                content = `[用户${source}]\n商品：${productName}\n价格：${productPrice}`;
            }
            
            debug('合并商品卡信息后的内容:', content);
        }

        return content;
    }

    // 生成消息ID的函数
    function generateMessageId(element) {
        const timestamp = Date.now();
        const randomStr = Math.random().toString(36).substring(2, 8);
        return `msg_${timestamp}_${randomStr}`;
    }

    // 消息类型常量
    const MESSAGE_TYPES = {
        TEXT: 'text',
        IMAGE: 'image',
        PRODUCT: 'product',
        ORDER: 'order',
        REFUND: 'refund',
        LEGOCARD: 'legocard',
        UNKNOWN: 'unknown'
    };



    // 获取商品卡片信息
    // 检查是否商品卡，并提取商品id，商品名称，商品价格，商品链接，商品图片
    function getProductCardInfo(element) {
        try {
            // 商品详情页进入，用户主动发商品卡咨询
            // 检查 msg-content good-card 格式
            const goodCard = element.querySelector('.buyer-item .msg-content.good-card');
            if (goodCard) {
                const info = {
                    goodsId: goodCard.querySelector('.good-id')?.textContent?.replace('商品ID：', '')?.replace('复制','')?.trim(),
                    goodsName: goodCard.querySelector('.good-name')?.textContent?.trim(),
                    type: 1
                };
                return info;
            }
            

            // 检查 notify-card 从商品详情页进入，默认的通知
            const goodContent = element.querySelector('.notify-card .good-content');
            if (goodContent) {
                
                //过滤售后方式的商品卡片
                if( element.querySelector('.notify-card .title')?.textContent?.includes('售后') ) {
                    return false;
                }


                return {
                    goodsName: goodContent.querySelector('p')?.textContent?.trim(),
                    type: 2
                };
                
            }

            //匹配商品规格卡
            const productSpec = element.querySelector('.msg-content.lego-card ');
            if (productSpec) {
                const skuCard = productSpec.querySelector('[class*="mallGoodsSkuCard"]');
                if(skuCard){
                    const goodsSku = productSpec.querySelector('[class*="goodsSku"]');
                    if(goodsSku){
                        //取数据
                        return {
                            goodsName: goodsSku.querySelector('[class*="goodsName"]')?.textContent,
                            skuName: goodsSku.querySelector('[class*="goodsSpec"]')?.textContent,
                            type: 3
                        };
                    }
                }
            }

            return false;
        } catch (error) {
            debug('获取商品信息异常:', error);
            return false;
        }
    }

    function checkProductAndText(element) {
        //上一个节点
        let before_dom = element.previousElementSibling;
        if(before_dom) {
            let res = getProductCardInfo(before_dom);
            if(res === false) {
                // //不是商品卡,继续取上一个
                // let before_dom2 = before_dom.previousElementSibling;
                // if(before_dom2) {
                //     let res2 = getProductCardInfo(before_dom2);
                //     if(res2 === false) {
                //         return false;
                //     }else {
                        
                //         return res2;
                //     }
                // }
                return false;
            }else {
                
                return res
            }
        }
    }
    function checkTextAndProduct(element) {
        //上一个节点
        let before_dom = element.previousElementSibling;
        if(before_dom) {
            let res = before_dom.querySelector('.buyer-item');
            if(res) {
                return res?.textContent;
            }
        }
        return false;
    }
    // 修改消息类型判断函数
    function determineMessageType(message) {
        if (!message?.element) return MESSAGE_TYPES.UNKNOWN;
        
        debug('开始判断消息类型');
        
        // 先检查售后/退款类型 - 提高优先级，确保这些类型被优先识别
        if (message.element.querySelector('.refund-card, .apply-card')) {
            debug('检测到退款/售后申请消息');
            return MESSAGE_TYPES.REFUND;
        }
        
        // 新增：检查commonCardTemp类型的售后卡片
        if (message.element.querySelector('.commonCardTemp') && 
            (message.element.querySelector('.commonCardTemp .title')?.textContent.includes('售后') ||
             message.element.querySelector('.commonCardTemp .text-content')?.textContent.includes('售后'))) {
            debug('检测到平台售后通知消息');
            return MESSAGE_TYPES.REFUND;
        }
        
        // 先检查商品卡
        const goodCard = message.element.querySelector('.msg-content.good-card');
        if (goodCard) {
            debug('检测到商品卡消息');
            return MESSAGE_TYPES.TEXT;  // 商品卡作为文本消息处理
        }
        
        // 再检查文本内容
        if (message.content && typeof message.content === 'string' && message.content.trim()) {
            debug('检测到文本消息');
            return MESSAGE_TYPES.TEXT;
        }
        
        // 最后检查其他类型
        if (message.element.querySelector('.order-card')) {
            debug('检测到订单消息');
            return MESSAGE_TYPES.ORDER;
        }
        
        if (message.element.querySelector('.image-msg, .video-content') || message.content?.trim() === '[图片]') {
            debug('检测到图片消息');
            return MESSAGE_TYPES.IMAGE;
        }
        
        // 检查是否是lego-card但不是apply-card (避免重复检查)
        if (message.element.querySelector('.lego-card') && !message.element.querySelector('.apply-card')) {
            debug('检测到lego卡片消息');
            return MESSAGE_TYPES.LEGOCARD;
        }
        
        debug('未能识别消息类型，标记为未知类型');
        return MESSAGE_TYPES.UNKNOWN;
    }

    // 处理单条消息
    async function processMessage(message, conversationId) {
        debug('[监听消息]开始处理消息:', message);
        try {
            // 检查自动回复状态
            if (!window.floatingBall.autoReplyEnabled) {
                debug('自动回复已关闭，跳过消息处理');
                return;
            }

            // 检查是否正在处理消息
            if (window.isProcessingMessage) {
                debug('消息处理中，跳过新的消息');
                return;
            }

            window.isProcessingMessage = true;
            window.lastMessageProcessingStartTime = Date.now(); // 记录消息处理开始时间
            
            if (!message?.element) {
                debug('消息无效，跳过处理');
                return;
            }

            const state = conversationStates.get(conversationId);
            if (!state) {
                debug('找不到会话状态:', conversationId);
                return;
            }
            
            // 检查是否是售后申请卡片，如果是则更新消息类型
            if (message.element.querySelector('.apply-card') && 
                (message.element.textContent.includes('消费者申请售后') || 
                 message.element.textContent.includes('退款') || 
                 message.element.textContent.includes('退货'))) {
                debug('[消息类型] 检测到售后申请卡片，更新消息类型为REFUND');
                message.type = MESSAGE_TYPES.REFUND;
            }
            
            // 检查是否是commonCardTemp类型的售后卡片
            if (message.element.querySelector('.commonCardTemp') && 
                (message.element.querySelector('.commonCardTemp .title')?.textContent.includes('售后') ||
                 message.element.querySelector('.commonCardTemp .text-content')?.textContent.includes('售后'))) {
                debug('[消息类型] 检测到平台售后通知消息，更新消息类型为REFUND');
                message.type = MESSAGE_TYPES.REFUND;
            }

            // 检查是否需要转人工
            const shouldTransfer = await checkShouldTransfer(message, state);
            
            // 检查是否包含转人工关键词，即使总开关关闭也需要检查
            const isTransferKeyword = message.content && 
                (message.content.includes('转人工') || 
                 message.content.includes('转接') || 
                 message.content.includes('转售前') || 
                 message.content.includes('转售后'));
                 
            // 如果是转人工关键词，标记为需要处理转人工
            let isHandledByTransfer = false;
                 
            if (shouldTransfer) {
                // if(shouldTransfer === 'cardMsg'){
                //     //卡片假消息，拦截
                //     return;
                // }
                debug('触发转人工条件，准备转人工');
                const transferSuccess = await handleTransfer(conversationId);
                if (transferSuccess) {
                    // 发送转人工成功提示消息
                    await sendMessage('收到，请您稍候一下···');
                    
                    // 标记所有未回复的消息为已回复
                    if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                        debug(`标记 ${message.unrepliedMessages.length} 条未回复消息为已回复`);
                        for (const msgElement of message.unrepliedMessages) {
                            await markMessageAsReplied(msgElement, conversationId);
                        }
                    } else {
                        // 如果没有未回复消息列表，标记当前消息为已回复
                        await markMessageAsReplied(message.element, conversationId);
                    }
                    
                    isHandledByTransfer = true;
                    return;
                } else {
                    // 转人工失败，检查是否需要自动打标
                    const settings = await StateManager.getState('transferSettings', {});
                    if (settings.transferFailMarkStar) {
                        debug('[转人工] 转人工失败，执行自动打标');
                        actionStarFlag();
                    }
                    //转失败，不继续处理
                    isHandledByTransfer = true;
                    return;
                }
            } else if (isTransferKeyword) {
                // 即使总开关关闭，也检查是否包含转人工关键词，如果包含则检查是否需要自动打标
                debug('[转人工] 检测到转人工关键词，但总开关已关闭');
                    
                    // 标记所有未回复的消息为已回复
                    if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                        debug(`标记 ${message.unrepliedMessages.length} 条未回复消息为已回复`);
                        for (const msgElement of message.unrepliedMessages) {
                            await markMessageAsReplied(msgElement, conversationId);
                        }
                    } else {
                        // 如果没有未回复消息列表，标记当前消息为已回复
                        await markMessageAsReplied(message.element, conversationId);
                    }
                    
                // 转人工关键词消息已在 checkShouldTransfer 函数中处理，这里直接返回，不再发送到 Dify
                isHandledByTransfer = true;
                    return;
                }
            // 如果已经被转人工逻辑处理过，不再继续处理
            if (isHandledByTransfer) {
                debug('消息已被转人工逻辑处理，不再继续处理');
                return;
            }
            
            let top_username = getTopUsername();
            if(top_username === 'youke') {
                debug('【游客】跳过游客消息类型回复');
                return;
            }

            // 如果不需要转人工继续处理消息
            // 根据消息类型处理
            debug("[AI设置] 消息类型", message.type);
            
            // 专门处理退款/售后类型消息
            if (message.type === MESSAGE_TYPES.REFUND) {
                debug('处理退款/售后类型消息');
                
                // 获取退款/售后专用表情
                const refundEmoji = getEmojiByType(MESSAGE_TYPES.REFUND); // 直接使用消息类型常量
                debug(`发送退款/售后表情回复: ${refundEmoji}`);
                
                try {
                    const success = await sendToPddCustomer(refundEmoji, true);
                    if (success) {
                        // 标记所有未回复的消息为已回复
                        if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                            debug(`标记 ${message.unrepliedMessages.length} 条未回复消息为已回复`);
                            for (const msgElement of message.unrepliedMessages) {
                                await markMessageAsReplied(msgElement, conversationId);
                            }
                        } else {
                            // 如果没有未回复消息列表，标记当前消息为已回复
                            await markMessageAsReplied(message.element, conversationId);
                        }
                        
                        state.processedMessages.add(message.id);
                        state.lastProcessedTime = Date.now();
                        floatingBallInstance?.setStatus('success');
                    } else {
                        floatingBallInstance?.setStatus('error');
                    }
                } catch (error) {
                    debug('发送退款/售后表情回复失败:', error);
                    floatingBallInstance?.setStatus('error');
                }
                
                return; // 处理完毕，不继续执行后续代码
            }
            
            // ===== 处理逻辑重构 =====
                         // 1. 优先处理商品卡消息
            if (message.productMessages && message.productMessages.length > 0 || 
                message.type === 'product' || message.productInfo) {
                
                 debug("优先处理商品卡消息");
                 
                // 先准备商品卡信息，尽早发送请求，但先检查是否已发送过
                 try {
                     const formattedUserInfo = getTopUsername();
                     
                    // 找到商品卡信息源
                    let productElement, productInfo, messageId;
                    
                    if (message.productMessages && message.productMessages.length > 0) {
                        productElement = message.productMessages[0];
                        productInfo = getProductCardInfo(productElement);
                        messageId = productElement.id || generateMessageId(productElement);
                    } else if (message.productInfo) {
                        productInfo = message.productInfo;
                        messageId = message.id || generateMessageId(message.element);
                    }
                    
                    // 检查消息是否已经处理过
                    const alreadyProcessed = await isMessageReplied(conversationId, messageId);
                    if (alreadyProcessed) {
                        debug('商品卡消息已处理过，跳过重复发送AI API请求');
                    } else if (productInfo) {
                     debug('后台发送商品卡信息到DIFY，不处理响应，用户标识:', formattedUserInfo);
                     
                         let productContent = '';
                         // 仅发送商品信息，不包含提问内容
                         if(productInfo.type === 1){
                             productContent = `咨询商品ID：${productInfo.goodsId}，商品名：${productInfo.goodsName}`;
                         } else if(productInfo.type === 3){
                             productContent = `商品名：${productInfo.goodsName}，规格：${productInfo.skuName}`;
                         } else if(productInfo.type === 2){
                             productContent = `商品名：${productInfo.goodsName}`;
                         }
                         
                         debug('商品卡仅发送商品信息，不包含任何提问内容');
                         
                        // 最优先：使用异步函数发送商品卡请求，不等待响应也不处理结果
                         sendDifyProductRequestAsync(productContent, formattedUserInfo, conversationId, state, message);
                     }
                 } catch (error) {
                     debug('处理商品卡信息失败:', error.message);
                 }
                 
                // 发送默认回复文案并立即标记为已回复，但先检查是否已回复
                if (message.productMessages && message.productMessages.length > 0) {
                    // 首先检查第一个商品卡是否已经回复过
                    const productElement = message.productMessages[0];
                    const messageId = productElement.id || generateMessageId(productElement);
                    
                    // 优先使用isMessageReplied检查，更准确地判断消息是否已回复
                    const alreadyReplied = await isMessageReplied(conversationId, messageId);
                    if (alreadyReplied) {
                        debug('商品卡消息已回复过，跳过重复回复，消息ID:', messageId);
                        
                        // 确保所有商品卡消息都标记为已回复
                        for (let i = 0; i < message.productMessages.length; i++) {
                            const msgId = message.productMessages[i].id || generateMessageId(message.productMessages[i]);
                            if (!await isMessageReplied(conversationId, msgId)) {
                                await saveRepliedMessage(conversationId, msgId);
                                debug('标记商品卡消息为已回复:', msgId);
                            }
                        }
                    } else {
                        // 如果没有回复过，发送默认回复
                        debug('商品卡消息未回复过，准备发送默认回复，消息ID:', messageId);
                        const replySuccess = await sendDefaultProductReply(productElement, conversationId);
                        if (replySuccess) {
                            debug('已发送商品卡默认回复');
                            
                            // 如果有多个商品卡消息，标记剩余的
                            if (message.productMessages.length > 1) {
                                debug(`标记剩余 ${message.productMessages.length - 1} 条商品卡消息为已回复`);
                                for (let i = 1; i < message.productMessages.length; i++) {
                                    const msgId = message.productMessages[i].id || generateMessageId(message.productMessages[i]);
                                    if (!await isMessageReplied(conversationId, msgId)) {
                                        await saveRepliedMessage(conversationId, msgId);
                                        debug('标记剩余商品卡消息为已回复:', msgId);
                                    }
                                }
                            }
                        }
                    }
                } else if (message.element && (message.type === 'product' || message.productInfo)) {
                    // 处理单个商品卡消息，先检查是否已回复
                    const messageId = message.id || generateMessageId(message.element);
                    
                    // 优先使用isMessageReplied检查
                    const alreadyReplied = await isMessageReplied(conversationId, messageId);
                    if (alreadyReplied) {
                        debug('单个商品卡消息已回复过，跳过重复回复，消息ID:', messageId);
                    } else {
                        // 如果没有回复过，发送默认回复
                        debug('单个商品卡消息未回复过，准备发送默认回复，消息ID:', messageId);
                        const replySuccess = await sendDefaultProductReply(message, conversationId);
                        if (replySuccess) {
                            debug('已发送单个商品卡默认回复');
                        }
                    }
                }
                
                // 2. 处理纯文本消息
                 if (message.textMessages && message.textMessages.length > 0) {
                     debug(`发现 ${message.textMessages.length} 条文本消息，准备处理`);
                     
                     try {
                         const formattedUserInfo = getTopUsername();
                         
                         // 创建一个新的仅包含文本消息的内容字符串
                         let combinedTextContent = '';
                         
                         // 从文本消息列表中提取所有内容并合并
                         for (const textElement of message.textMessages) {
                             const messageContent = extractMessageContent(textElement);
                             if (messageContent && messageContent.trim()) {
                                 if (combinedTextContent) {
                                     combinedTextContent = combinedTextContent + '\n' + messageContent;
                                 } else {
                                     combinedTextContent = messageContent;
                                 }
                                 debug('添加文本消息内容:', messageContent);
                             }
                         }
                         
                         debug('发送合并后的文本消息给DIFY，用户标识:', formattedUserInfo);
                         debug('合并后的纯文本内容:', combinedTextContent);
                         
                         // 等待商品卡请求完成，确保会话ID已获取
                         if (productCardRequestPromise) {
                             debug('等待商品卡请求完成以获取会话ID...');
                             try {
                                 const productCardResult = await Promise.race([
                                     productCardRequestPromise,
                                     // 设置5秒超时，避免无限等待
                                     new Promise((_, reject) => setTimeout(() => reject(new Error('等待商品卡请求超时')), 5000))
                                 ]);
                                 
                                 if (productCardResult && productCardResult.conversation_id) {
                                     debug('成功获取商品卡会话ID:', productCardResult.conversation_id);
                                 } else {
                                     debug('商品卡请求已完成，但未返回会话ID');
                                 }
                             } catch (timeoutError) {
                                 debug('等待商品卡请求超时或失败:', timeoutError.message);
                             }
                         } else {
                             debug('没有正在进行的商品卡请求');
                         }
                         
                         // 发送并等待响应
                         debug('发送文本消息请求并等待响应');
                         const response = await sendDifyTxtRequest(combinedTextContent, formattedUserInfo, conversationId, state, message);
                         
                         debug('文本消息处理完成，收到响应:', response);
                         
                         // 标记所有文本消息为已回复
                         for (const textElement of message.textMessages) {
                             await markMessageAsReplied(textElement, conversationId);
                         }
                     } catch (error) {
                         debug('文本消息处理失败:', error.message);
                     }
                 }
                 
                 // 3. 标记其他类型消息为已回复
                 if (message.otherMessages && message.otherMessages.length > 0) {
                     debug(`标记 ${message.otherMessages.length} 条其他类型消息为已回复`);
                     for (const otherElement of message.otherMessages) {
                         await markMessageAsReplied(otherElement, conversationId);
                     }
                 }
                 
                 return;
            }
            // 处理商品卡相关类型 - 通过上面优化的处理流程已经处理，不再重复处理
            else if (message.type === MESSAGE_TYPES.PRODUCT || message.type === 'ProductAndText' || message.type === 'TextAndProduct') {
                debug('商品卡类型消息已在新的处理流程中处理，跳过此分支');
                // 不执行任何操作，避免重复处理
                return;
            } 
            // 处理文本类消息
            else if ((message.type === MESSAGE_TYPES.TEXT || message.type === MESSAGE_TYPES.UNKNOWN) && message.content) {
                //添加备注检查及处理
                if( await checkBackupKeywordEnabled() ) {
                    if ( await checkBackupKeyword(message.content) ) {
                        let order_item = await findBackupKeywordOrderItem();
                        if(order_item) {
                            if(await actionBackupKeyword(message.content, order_item)) {
                                await sendMessage('好的，我帮您添加备注');
                                
                                // 标记所有未回复的消息为已回复
                                if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                                    debug(`标记 ${message.unrepliedMessages.length} 条未回复消息为已回复`);
                                    for (const msgElement of message.unrepliedMessages) {
                                        await markMessageAsReplied(msgElement, conversationId);
                                    }
                                } else {
                                    // 如果没有未回复消息列表，标记当前消息为已回复
                                    await markMessageAsReplied(message.element, conversationId);
                                }
                                
                                return;
                            }
                        }
                    }
                }
                
                //打星标处理
                if( await checkStarFlagEnabled() ) {
                    if ( await checkStarFlagKeyword(message.content) ) {
                        //打星标
                        actionStarFlag();
                        await sendMessage('好的，我安排专人跟进处理一下');
                        
                        // 标记所有未回复的消息为已回复
                        if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                            debug(`标记 ${message.unrepliedMessages.length} 条未回复消息为已回复`);
                            for (const msgElement of message.unrepliedMessages) {
                                await markMessageAsReplied(msgElement, conversationId);
                            }
                        } else {
                            // 如果没有未回复消息列表，标记当前消息为已回复
                            await markMessageAsReplied(message.element, conversationId);
                        }
                        
                        return;
                    }
                }

                // 文本消息和未知类型消息都发送到 Dify 处理
                try {
                    // 使用格式化的用户标识（客服账号+昵称+用户ID）
                    const formattedUserInfo = getTopUsername();
                    debug('发送给DIFY的用户标识:', formattedUserInfo);
                    const response = await sendDifyTxtRequest(message.content, formattedUserInfo, conversationId, state, message);
                    
                    debug('文本消息处理完成:', response);
                    
                    // 检查是否已经在difyReplyProcess中标记为已回复
                    if (!response.messageMarkedInDifyProcess) {
                        // 只有在difyReplyProcess中没有标记过的情况下，才进行标记
                        // 标记所有未回复的消息为已回复
                        if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                            debug(`标记 ${message.unrepliedMessages.length} 条未回复消息为已回复`);
                            for (const msgElement of message.unrepliedMessages) {
                                // 检查是否已经在difyReplyProcess中标记过
                                if (!msgElement._markedAsReplied) {
                                    await markMessageAsReplied(msgElement, conversationId);
                                } else {
                                    debug('该消息已有回复标记');
                                    // 确保有视觉标记
                                    await addVisualReplyMark(msgElement);
                                }
                            }
                        } else {
                            // 如果没有未回复消息列表，标记当前消息为已回复
                            if (!message.element._markedAsReplied) {
                                await markMessageAsReplied(message.element, conversationId);
                            } else {
                                debug('该消息已有回复标记');
                                // 确保有视觉标记
                                await addVisualReplyMark(message.element);
                            }
                        }
                    } else {
                        // 即使在difyReplyProcess中已标记，也确保有视觉标记
                        if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                            for (const msgElement of message.unrepliedMessages) {
                                await addVisualReplyMark(msgElement);
                            }
                        } else if (message.element) {
                            await addVisualReplyMark(message.element);
                        }
                    }
                } catch (error) {
                    debug('文本消息处理失败:', error.message);
                    floatingBallInstance?.setStatus('error');
                    // 发送默认回复作为降级处理
                    const success = await sendMessage('好的，我已收到您的消息');
                }
            }else if(message.type === MESSAGE_TYPES.IMAGE) {
                //图片处理
                let config = await StateManager.getState('aiSettings', {});
                
                //判断是否开启图片识别
                
                const aiImageEnabled = config.aiImageEnabled;
                if(aiImageEnabled){
                    //开启图片识别
                    // 使用格式化的用户标识（客服账号+昵称+用户ID）
                    const formattedUserInfo = getTopUsername();
                    debug('发送给DIFY的用户标识:', formattedUserInfo);
                    await sendDifyImageRequest(message.element.querySelector('.image-msg img')?.src, formattedUserInfo);
                    
                    // 标记所有未回复的消息为已回复
                    if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                        debug(`标记 ${message.unrepliedMessages.length} 条未回复消息为已回复`);
                        for (const msgElement of message.unrepliedMessages) {
                            await markMessageAsReplied(msgElement, conversationId);
                        }
                    } else {
                        // 如果没有未回复消息列表，标记当前消息为已回复
                        await markMessageAsReplied(message.element, conversationId);
                    }
                    
                }else{
                    debug('[AI设置] 关闭图片识别');
                    const emoji = getEmojiByType(message.type);
                    debug(`发送表情回复: ${emoji}`);
                    try {
                        const success = await sendToPddCustomer(emoji, true);
                        if (success) {
                            // 标记所有未回复的消息为已回复
                            if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                                debug(`标记 ${message.unrepliedMessages.length} 条未回复消息为已回复`);
                                for (const msgElement of message.unrepliedMessages) {
                                    await markMessageAsReplied(msgElement, conversationId);
                                }
                            } else {
                                // 如果没有未回复消息列表，标记当前消息为已回复
                                await markMessageAsReplied(message.element, conversationId);
                            }
                            
                            state.processedMessages.add(message.id);
                            state.lastProcessedTime = Date.now();
                            floatingBallInstance?.setStatus('success');
                        }
                    } catch (error) {
                        debug('发送表情回复失败:', error);
                        floatingBallInstance?.setStatus('error');
                    }
                }
            
            }else if(message.type === MESSAGE_TYPES.LEGOCARD) {
                debug('[转人工] 进入改地址');

                //修改地址卡片，以及修改成功卡片
                let is_modify_address_card = await modifyAddressCheck(message.element);
                debug('[转人工] 改地址返回值',is_modify_address_card);
                
                // 处理自动同意修改地址功能已关闭的情况
                if (is_modify_address_card && typeof is_modify_address_card === 'object' && is_modify_address_card.type === 'disabled') {
                    debug('[改地址] 自动同意修改地址功能已关闭，发送自定义提示语');
                    await sendMessage(is_modify_address_card.message);
                    
                    // 标记所有未回复的消息为已回复
                    if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                        debug(`标记 ${message.unrepliedMessages.length} 条未回复消息为已回复`);
                        for (const msgElement of message.unrepliedMessages) {
                            await markMessageAsReplied(msgElement, conversationId);
                        }
                    } else {
                        // 如果没有未回复消息列表，标记当前消息为已回复
                        await markMessageAsReplied(message.element, conversationId);
                    }
                    
                    return;
                }
                
                if(is_modify_address_card === 'transfer') {
                    //非待发货修改地址，转人工
                    // 获取设置
                    const transferSettings = await StateManager.getState('transferSettings', {
                        manualEnabled: true  // 总开关
                    });
                    // 首先检查总开关状态
                    if (!transferSettings.manualEnabled) {
                        debug('[转人工] 非待发货修改地址，转人工，总开关已关闭');
                        // 总开关关闭时，仍然告知用户订单已发货无法修改
                        await sendMessage('订单已发货，无法修改地址');
                        
                        // 标记所有未回复的消息为已回复
                        if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                            debug(`标记 ${message.unrepliedMessages.length} 条未回复消息为已回复`);
                            for (const msgElement of message.unrepliedMessages) {
                                await markMessageAsReplied(msgElement, conversationId);
                            }
                        } else {
                            // 如果没有未回复消息列表，标记当前消息为已回复
                            await markMessageAsReplied(message.element, conversationId);
                        }
                        
                        return;
                    }

                    // 总开关开启，尝试转人工
                    debug('[转人工] 非待发货修改地址，尝试转人工');
                    const transferSuccess = await handleTransfer(conversationId);
                    if (transferSuccess) {
                        // 发送转人工成功提示消息
                        await sendMessage('已经为您转接客服处理了，请稍候...');
                    } else {
                        // 转人工失败，告知用户订单已发货无法修改
                        debug('[转人工] 转人工失败');
                        await sendMessage('订单已发货，无法修改地址');
                    }
                    
                    // 标记所有未回复的消息为已回复
                    if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                        debug(`标记 ${message.unrepliedMessages.length} 条未回复消息为已回复`);
                        for (const msgElement of message.unrepliedMessages) {
                            await markMessageAsReplied(msgElement, conversationId);
                        }
                    } else {
                        // 如果没有未回复消息列表，标记当前消息为已回复
                        await markMessageAsReplied(message.element, conversationId);
                    }
                    
                    return;
                }else if(is_modify_address_card === true) {
                    //待发货修改地址，已处理
                    
                    // 标记所有未回复的消息为已回复
                    if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                        debug(`标记 ${message.unrepliedMessages.length} 条未回复消息为已回复`);
                        for (const msgElement of message.unrepliedMessages) {
                            await markMessageAsReplied(msgElement, conversationId);
                        }
                    } else {
                        // 如果没有未回复消息列表，标记当前消息为已回复
                        await markMessageAsReplied(message.element, conversationId);
                    }
                    
                    return;
                }else if(is_modify_address_card === 'rejected') {
                    //已发货修改地址，已自动拒绝
                    debug('[改地址] 已自动拒绝修改地址请求');
                    await sendMessage('订单已发货，无法修改地址');
                    
                    // 标记所有未回复的消息为已回复
                    if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                        debug(`标记 ${message.unrepliedMessages.length} 条未回复消息为已回复`);
                        for (const msgElement of message.unrepliedMessages) {
                            await markMessageAsReplied(msgElement, conversationId);
                        }
                    } else {
                        // 如果没有未回复消息列表，标记当前消息为已回复
                        await markMessageAsReplied(message.element, conversationId);
                    }
                    
                    return;
                }
                //const card = message.element.querySelector('.lego-card');
                return;
            } else {
                // 其他类型消息处理
                const emoji = getEmojiByType(message.type);
                debug(`发送表情回复: ${emoji}`);
                
                try {
                    const success = await sendToPddCustomer(emoji, true);
                    if (success) {
                        // 标记所有未回复的消息为已回复
                        if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                            debug(`标记 ${message.unrepliedMessages.length} 条未回复消息为已回复`);
                            for (const msgElement of message.unrepliedMessages) {
                                await markMessageAsReplied(msgElement, conversationId);
                            }
                        } else {
                            // 如果没有未回复消息列表，标记当前消息为已回复
                            await markMessageAsReplied(message.element, conversationId);
                        }
                        
                        state.processedMessages.add(message.id);
                        state.lastProcessedTime = Date.now();
                        floatingBallInstance?.setStatus('success');
                    } else {
                        floatingBallInstance?.setStatus('error');
                    }
                } catch (error) {
                    debug('发送表情回复失败:', error);
                    floatingBallInstance?.setStatus('error');
                }
            }
        } catch (error) {
            console.error('处理消息时出错:', error);
            floatingBallInstance?.setStatus('error');
        } finally {
            window.isProcessingMessage = false;
            window.lastMessageProcessingStartTime = null;
            
            // 如果没有其他消息在处理，重置全局状态
            if (isProcessing) {
                isProcessing = false;
                jump_listenid_count = 0;
                window.lastProcessingStartTime = null;
            }
        }
    }

    // 检查是否需要转人工
    /**
     * 检查是否需要转人工，转人工操作的前置判断，所有判断的入口
     * @param {*} message 
     * @param {*} state 
     * @returns 
     * comment by rice 2025-01-11
     */
    async function checkShouldTransfer(message, state) {
        // 获取设置
        const settings = await StateManager.getState('transferSettings', {
            manualEnabled: true,  // 总开关
            keywordEnabled: true, // 关键词触发
            imageEnabled: true,   // 图片触发
            refundEnabled: true,  // 退款触发
            complaintEnabled: true // 投诉触发
        });
        
        // 检查是否包含转人工关键词
        const isTransferKeyword = message.content && 
            (message.content.includes('转人工') || 
             message.content.includes('转接') || 
             message.content.includes('转售前') || 
             message.content.includes('转售后'));
        
        // 首先检查总开关状态
        if (!settings.manualEnabled) {
            debug('[转人工] 自动转人工总开关已关闭');
            // 只有当消息包含转人工关键词时才处理
            if (isTransferKeyword) {
                debug('[转人工] 检测到转人工关键词，处理已关闭自动转人工的情况');
                return await handleTransferClosed();
            }
            return false;
        }

        // 检查会话状态
        // const transferState = TransferManager.getTransferState(state.getPddConversationId());
        // if (transferState === TransferState.TRANSFERRED || 
        //     transferState === TransferState.TRANSFERRING) {
        //     debug('[转人工] 该会话已经转人工或正在转人工中');
        //     return false;
        // }


        // 只有在总开关开启的情况下，才检查各个子开关
        if (settings.manualEnabled) {
            // 关键词触发转人工
            if (settings.keywordEnabled && message.content) {
                const keywords = await loadTransferKeywords();
                if (keywords.some(keyword => message.content.includes(keyword))) {
                    debug('[转人工] 检测到转人工关键词，触发转人工');
                    return true;
                }
            }

            // 图片消息转人工
            if (settings.imageEnabled && message.type === MESSAGE_TYPES.IMAGE) {
                debug('[转人工] 检测到图片消息，触发转人工');
                return true;
            }

            // 退款场景转人工
            if (settings.refundEnabled && 
                (message.type === MESSAGE_TYPES.REFUND )) {
                debug('[转人工] 检测到退款相关内容，触发转人工');
                return true;
            }

            // 投诉场景转人工
            if (settings.complaintEnabled && message.content) {
                const complaintKeywords = ['投诉', '差评', '不满意', '质量差', '态度差'];
                if (complaintKeywords.some(keyword => message.content.includes(keyword))) {
                    debug('[转人工] 检测到投诉相关内容，触发转人工');
                    return true;
                }
            }

            //增加修改地址转人工处理
            //comment by rice 2025-01-11
            // if(message.type === MESSAGE_TYPES.LEGOCARD){
            //     const modifyAddressCardMsg = await loadModifyAddressCardMsg();
            //     debug('[转人工] 检测到修改地址卡片消息', modifyAddressCardMsg);
            //     if(modifyAddressCardMsg !== false) {
            //         //返回true 或者 cardMsg
            //         return modifyAddressCardMsg;
            //     }
            // }
        }




        return false;
    }

    // 标记消息为已回复
    async function markMessageAsReplied(messageElement, conversationId) {
        if (!messageElement || !conversationId) return;
        
        // 使用统一的检查函数
        if (await hasReplyMark(messageElement, conversationId)) {
            debug('该消息已有回复标记');
            
            // 增加计数器
            replyMarkCounter++;
            debug(`[回复标记] 该消息已有回复标记计数: ${replyMarkCounter}/${MAX_COUNTER}`);
            
            // 获取当前消息内容用于检测重复
            try {
                const msgContent = messageElement.querySelector('.msg-content-box, .text-content');
                if (msgContent) {
                    const currentContent = msgContent.textContent.trim();
                    
                    // 检查是否与上一条消息内容相同
                    if (currentContent === lastMessageContent && currentContent) {
                        repeatContentCounter++;
                        debug(`[重复内容] 检测到重复内容，计数: ${repeatContentCounter}/${MAX_COUNTER}`);
                    } else {
                        // 内容不同，重置重复计数器
                        repeatContentCounter = 0;
                        // 更新最后一条消息内容
                        lastMessageContent = currentContent;
                    }
                }
            } catch (error) {
                debug('[重复内容] 检查重复内容时出错:', error);
            }
            
            // 如果计数达到阈值且内容重复也达到阈值，刷新页面
            if (replyMarkCounter >= MAX_COUNTER && repeatContentCounter >= MAX_COUNTER) {
                debug('[回复标记] 检测到多次重复回复标记且内容重复，准备刷新页面');
                setTimeout(() => {
                    debug('[回复标记] 执行页面刷新...');
                    window.location.reload();
                }, 500);
                return;
            }
            // 如果只有回复标记计数达到阈值，执行健康检查
            else if (replyMarkCounter >= MAX_COUNTER) {
                debug('[回复标记] 检测到多次重复回复标记，执行健康检查');
                performHealthCheck();
                // 重置计数器
                replyMarkCounter = 0;
            }
            
            return;
        }
        
        try {
            // 生成消息ID并存储到Chrome Storage
            const messageId = messageElement.id || generateMessageId(messageElement);
            
            // 存储到Chrome Storage
            await saveRepliedMessage(conversationId, messageId);
            
            // 添加视觉标记（绿色勾号）
            await addVisualReplyMark(messageElement);
            
        } catch (error) {
            debug('添加回复标记时出错:', error);
        }
    }

    // 定时检查未读消息列表
    // 防多次注入
    if (!window.hasRun) {
        window.hasRun = true;
        // content script 的代码，包括 setInterval
        // ... 其他代码 ...
        debug('[listenToPDDMessages] 启动未读消息监听');
        checkUnreadMessages_listenid = setInterval(checkUnreadMessages, 1000);
        // ... 其他代码 ...
    }

    // 监听红点通知
    function checkNotification() {
        const notificationElement = document.evaluate(
            '//div/div[1]/ul/ul/li//i',
            document,
            null,
            XPathResult.FIRST_ORDERED_NODE_TYPE,
            null
        ).singleNodeValue;

        if (notificationElement) {
            const parentElement = notificationElement.closest('div');
            if (parentElement) {
                parentElement.click();
            }
        }
    }


    // 调用创建函数
    window.createHealthCheckTimer();

    // 定时检查红点通知
    debug('[checkNotification] 启动红点通知监听');
    checkNotification_listenid = setInterval(checkNotification, 1000);


    // 页面卸载时清理所有定时器和状态
    const cleanupHandler = () => {
        debug('页面卸载，开始清理资源');
        
        // 清理所有定时器
        if (window.statusMonitorTimer) {
            clearInterval(window.statusMonitorTimer);
            window.statusMonitorTimer = null;
        }
        
        if (checkUnreadMessages_listenid > 0) {
            clearInterval(checkUnreadMessages_listenid);
            checkUnreadMessages_listenid = 0;
        }
        
        if (checkNotification_listenid > 0) {
            clearInterval(checkNotification_listenid);
            checkNotification_listenid = 0;
        }
        
        // 清理健康检查定时器
        if (window.healthCheckTimer) {
            clearInterval(window.healthCheckTimer);
            window.healthCheckTimer = null;
        }
        window.healthCheckTimerCreating = false;
        
        // 重置所有状态
        isProcessing = false;
        jump_listenid_count = 0;
        window.lastProcessingStartTime = null;
        window.isProcessingMessage = false;
        window.lastMessageProcessingStartTime = null;
        window.hasRun = false;
        
        debug('资源清理完成');
    };

    window.addEventListener('beforeunload', cleanupHandler);
    window.addEventListener('unload', cleanupHandler);
}



// 监听来自 background script 的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.type === 'pddResponse') {
        debug('收到来自background的回复:', message);
        
        // 发送AI回复到聊天框
        sendToPddCustomer(message.response, true).then(success => {
            if (success) {
                debug('AI回复已放入聊天框');
                sendResponse({ success: true });
            } else {
                debug('发送AI回复失败');
                sendResponse({ success: false, error: 'Failed to send message' });
            }
        });
        
        return true; // 保持消息通道开放
    }
});




// 发送状态更新到窗口
function updateStatus(status, details = '') {
    chrome.runtime.sendMessage({
        type: 'statusUpdate',
        status,
        details,
        timestamp: new Date().toLocaleTimeString()
    });
}

// 获取会话ID的函数
function getConversationId(chatItem) {
    try {
        // 首先尝试从chat-item本身获取data-random属性
        let conversationId = chatItem?.getAttribute('data-random');
        
        if (!conversationId) {
            // 如果chat-item上没有，尝试从chat-item-box获取
            const chatItemBox = chatItem?.querySelector('.chat-item-box');
            conversationId = chatItemBox?.getAttribute('data-random');
        }
        
        if (!conversationId) {
            // 如果还是没有，尝试从父元素获取
            const parentWithRandom = chatItem?.closest('[data-random]');
            conversationId = parentWithRandom?.getAttribute('data-random');
        }

        // 如果找到了data-random属性，从中提取数字部分作为用户ID
        if (conversationId) {
            // 尝试提取数字ID部分（例如从"6484435977599-0-unTimeout"中提取"6484435977599"）
            const numberId = extractNumericUserId(conversationId);
            if (numberId) {
                debug('从data-random属性提取到用户ID:', numberId);
                return numberId;
            }
        }

        if (!conversationId) {
            // 如果还是没有，尝试从id属性中提取
            const id = chatItem?.id;
            if (id?.includes('_')) {
                const parts = id.split('_');
                conversationId = parts[parts.length - 1];
            }
        }
        
        if (!conversationId) {
            debug('无法获取会话ID，chatItem:', chatItem);
            // 生成一个临时ID
            return `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        
        return conversationId;
    } catch (error) {
        debug('获取会话ID时出错:', error);
        // 生成一个临时ID
        return `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}

// 从data-random属性中提取数字用户ID
function extractNumericUserId(dataRandomValue) {
    if (!dataRandomValue) return null;
    
    // 尝试匹配数字部分（如"6484435977599-0-unTimeout"中的"6484435977599"）
    const match = dataRandomValue.match(/^(\d+)/);
    if (match && match[1]) {
        return match[1];
    }
    
    return null;
}

// 弹窗配置
const POPUP_CONFIG = {
    SERVICE_ATTITUDE: {
        className: 'repeat-interceptor-popup',
        patterns: ['服务态度提醒', '相同内容的消息'],
        buttonSelector: '.el-button--default.el-button--mini'
    }
};

// 添加新消息动画效果
async function addMessageHighlight(messageElement) {
    if (!messageElement) return;
    
    // 查找消息气泡元素
    const bubbleElement = messageElement.querySelector('.msg-content-box, .text-content');
    if (!bubbleElement) {
        debug('未找到消息气泡元素');
        return;
    }
    
    // 创建动画样式
    const originalStyle = bubbleElement.style.cssText || '';
    bubbleElement.style.cssText = `
        ${originalStyle}
        position: relative;
        animation: messageBubbleHighlight 2s ease-in-out;
    `;

    // 添加动画关键帧
    const styleSheet = document.createElement('style');
    styleSheet.textContent = `
        @keyframes messageBubbleHighlight {
            0% {
                outline: 2px solid rgba(255, 0, 0, 0.4);
                outline-offset: 0px;
            }
            50% {
                outline: 2px solid rgba(255, 0, 0, 0.8);
                outline-offset: 4px;
            }
            100% {
                outline: 2px solid rgba(255, 0, 0, 0);
                outline-offset: 0px;
            }
        }
    `;
    document.head.appendChild(styleSheet);

    // 2秒后移除动画
    setTimeout(() => {
        bubbleElement.style.cssText = originalStyle;
        styleSheet.remove();
    }, 2000);
}

// 悬浮球状态管理
class FloatingBall {
    constructor() {
        this.ball = null;
        this.isDragging = false;
        this.startX = 0;
        this.startY = 0;
        this.ballRect = null;
        this.currentStatus = null;
        this.menuVisible = false;
        this.autoReplyEnabled = false;  //默认关闭自动回复
        
        // 绑定方法
        this.onMouseDown = this.onMouseDown.bind(this);
        this.onMouseMove = this.onMouseMove.bind(this);
        this.onMouseUp = this.onMouseUp.bind(this);

    }

    create() {
        try {
            // 检查是否已存在浮球
            if (document.querySelector('.floating-ball')) {
                console.log('[浮球] 浮球已存在');
                return;
            }

        // 创建浮球容器
        this.ball = document.createElement('div');
            this.ball.className = 'floating-ball auto-reply-active';
        this.ball.innerHTML = `
                <div class="icon-wrapper">
                    <img src="${chrome.runtime.getURL('icons/icon48.png')}" alt="AI助手" />
                </div>
                <div class="floating-menu">
                    <div class="menu-item" data-action="toggle-auto-reply">
                        <span class="menu-icon">🤖</span>
                        <span class="status-text">自动回复已开启</span>
                        <span class="toggle-switch"></span>
                    </div>
                    <div class="menu-divider"></div>
                    <div class="menu-item" data-action="ai-settings">
                        <span class="menu-icon">⚛️</span>
                        <span>AI设置</span>
                    </div>
                    <div class="menu-item" data-action="settings">
                        <span class="menu-icon">🔁</span>
                        <span>转人工设置</span>
                    </div>
                    <div class="menu-item" data-action="other-settings">
                        <span class="menu-icon">🎛️</span>
                        <span>其他设置</span>
                    </div>
                    <div class="menu-item" data-action="about">
                        <span class="menu-icon">ℹ️</span>
                        <span>关于</span>
                    </div>
            </div>
        `;

            // 添加到页面
            document.body.appendChild(this.ball);

            // 设置事件监听
            this.setupEventListeners();

            // 初始化状态
            this.initState();

            console.log('[浮球] 浮球创建成功');
        } catch (error) {
            console.error('[浮球] 创建失败:', error);
        }
    }

    setupEventListeners() {
        // 拖拽事件
        this.ball.addEventListener('mousedown', this.onMouseDown);
        
        // 菜单显示/隐藏
        this.ball.addEventListener('mouseenter', () => this.toggleMenu(true));
        this.ball.addEventListener('mouseleave', () => this.toggleMenu(false));
        
        // 菜单项点击
        const menu = this.ball.querySelector('.floating-menu');
        menu.addEventListener('click', (e) => {
            const menuItem = e.target.closest('.menu-item');
            if (menuItem) {
                e.stopPropagation();
                this.handleMenuClick(e);
            }
        });
    }

    onMouseDown(e) {
        if (e.target.closest('.menu-item') || e.target.closest('.floating-menu')) return;
        
        this.isDragging = true;
        this.ballRect = this.ball.getBoundingClientRect();
        this.startX = e.clientX - this.ballRect.left;
        this.startY = e.clientY - this.ballRect.top;
        
        this.ball.style.transition = 'none';
        document.addEventListener('mousemove', this.onMouseMove);
        document.addEventListener('mouseup', this.onMouseUp);
    }

    onMouseMove(e) {
        if (!this.isDragging) return;
        
        const x = e.clientX - this.startX;
        const y = e.clientY - this.startY;
        
        const maxX = window.innerWidth - this.ball.offsetWidth;
        const maxY = window.innerHeight - this.ball.offsetHeight;
        
        this.ball.style.left = Math.min(Math.max(0, x), maxX) + 'px';
        this.ball.style.top = Math.min(Math.max(0, y), maxY) + 'px';
        this.ball.style.right = 'auto';
        this.ball.style.transform = 'none';
    }

    onMouseUp() {
        this.isDragging = false;
        this.ball.style.transition = '';
        document.removeEventListener('mousemove', this.onMouseMove);
        document.removeEventListener('mouseup', this.onMouseUp);
    }

    toggleMenu(show = null) {
        const menu = this.ball.querySelector('.floating-menu');
        this.menuVisible = show !== null ? show : !this.menuVisible;
        
        if (this.menuVisible) {
            menu.style.display = 'block';
            menu.offsetHeight; // 强制重绘
            menu.classList.add('visible');
        } else {
            menu.classList.remove('visible');
            setTimeout(() => {
                if (!this.menuVisible) {
                    menu.style.display = 'none';
                }
            }, 300);
        }
    }

    handleMenuClick(e) {
        const menuItem = e.target.closest('.menu-item');
        if (!menuItem) return;
        
        const action = menuItem.dataset.action;
        switch (action) {
            case 'toggle-auto-reply':
                this.toggleAutoReply();
                break;
                
            case 'ai-settings':
                this.showAISettings();
                break;
                
            case 'settings':
                showSettingsWindow();
                break;
            case 'other-settings':
                showOtherSettingsWindow();
                break;
            case 'about':
                showAboutWindow();
                break;
        }
    }

    
    async initState() {
        //初始化自动回复状态,从storage中获取
        const enabled = await StateManager.getState('autoReplyEnabled', false);
        this.autoReplyEnabled = enabled;
        this.updateUIStatus(enabled);
    }

    updateUIStatus(enabled) {
        if (this.ball) {
            const statusText = this.ball.querySelector('.status-text');
            const toggle = this.ball.querySelector('.toggle-switch');
            
            if (enabled) {
                this.ball.classList.add('auto-reply-active');
                if (statusText) statusText.textContent = '自动回复已开启';
                if (toggle) toggle.classList.add('active');
            } else {
                this.ball.classList.remove('auto-reply-active');
                if (statusText) statusText.textContent = '自动回复已关闭';
                if (toggle) toggle.classList.remove('active');
            }
        }
    }
    //切换自动回复状态事件
    async toggleAutoReply() {
        const currentState = this.autoReplyEnabled;
        StateManager.setState('autoReplyEnabled', !currentState);
        this.autoReplyEnabled = !currentState;
        this.updateUIStatus(this.autoReplyEnabled);
        
        // 使用统一的页面清理管理器来清理现有的监听器和状态
        PageCleanupManager.ensureInitialized();
        // 手动触发清理逻辑（不是页面卸载，而是状态切换时的清理）
        if (window.statusMonitorTimer) {
            clearInterval(window.statusMonitorTimer);
            window.statusMonitorTimer = null;
        }
        
        if (checkUnreadMessages_listenid > 0) {
            clearInterval(checkUnreadMessages_listenid);
            checkUnreadMessages_listenid = 0;
        }
        
        if (checkNotification_listenid > 0) {
            clearInterval(checkNotification_listenid);
            checkNotification_listenid = 0;
        }
        
        // 重置健康检查定时器和创建标志
        if (window.healthCheckTimer) {
            clearInterval(window.healthCheckTimer);
            window.healthCheckTimer = null;
        }
        window.healthCheckTimerCreating = false;
        
        // 重置处理状态
        isProcessing = false;

        // 根据开关状态设置window.hasRun标志
        if (this.autoReplyEnabled) {
            // 开启总开关时，重置window.hasRun为false，确保能启动监听
            window.hasRun = false;
            debug('[toggleAutoReply] 开启总开关，重置 window.hasRun = false');
        } else {
            // 关闭总开关时，设置window.hasRun为true，防止意外启动
            window.hasRun = true;
            debug('[toggleAutoReply] 关闭总开关，设置 window.hasRun = true');
        }

        // 启动消息监听
        startPdd();
    }

    setStatus(status, duration = 1500) {
        if (this.currentStatus === status) return;
        
        this.currentStatus = status;
        if (this.ball) {
            // 移除所有状态类
            this.ball.classList.remove('status-processing', 'status-success', 'status-warning', 'status-error');
            
            if (status) {
                this.ball.classList.add(`status-${status}`);
                
                // 更新爱嘉浮窗图标外观
                const iconWrapper = this.ball.querySelector('.icon-wrapper');
                switch(status) {
                    case 'success':
                        iconWrapper.style.backgroundColor = '#52c41a';
                        break;
                    case 'error':
                        iconWrapper.style.backgroundColor = '#f5222d';
                        break;
                    case 'processing':
                        iconWrapper.style.backgroundColor = '#1890ff';
                        break;
                    default:
                        iconWrapper.style.backgroundColor = '';
                }

                if (status !== 'processing') {
                    setTimeout(() => {
                        this.ball.classList.remove(`status-${status}`);
                        this.currentStatus = null;
                        iconWrapper.style.backgroundColor = this.autoReplyEnabled ? '#1890ff' : '';
                    }, duration);
                }
            }
        }
    }



    // 添加 AI 设置对话框
    async showAISettings() {
        // 先从后台获取当前配置
        let config = await StateManager.getState('aiSettings', {});
        if(!config || !config.apiKey){
            config = await new Promise(resolve => {
                    chrome.runtime.sendMessage({ type: 'getInitialDifyConfig' }, response => {
                        if (chrome.runtime.lastError) {
                            debug('Chrome runtime错误:', chrome.runtime.lastError);
                            reject(new Error(chrome.runtime.lastError.message));
                            // 执行清理逻辑
                            return;
                        }
                        resolve(response?.success ? response.data : null);
                    });
                });
        }

        const aiSettingsContent = `
            <div class="settings-container">
                <div class="section">
                    <div class="section-title">AI 接口设置</div>
                    <div class="option-group">
                        
                        
                        <div class="option-item">
                            <div class="option-label">
                                AI API Key
                                <div class="option-description">设置 AI API 的访问密钥</div>
                            </div>
                            <input type="text" id="difyApiKey" class="settings-input" value="${config?.apiKey || ''}">
                        </div>
                    </div>
                </div>

                <div class="section">
                    <div class="section-title">AI 回复设置</div>
                    <div class="option-group">
                        <div class="option-item">
                            <div class="option-label">
                                最大响应时间
                                <div class="option-description">设置 AI 响应的最大等待时间（秒）</div>
                            </div>
                            <input type="number" id="maxResponseTime" class="settings-input" min="1" max="60" value="${config?.maxResponseTime || 30}">
                        </div>
                        
                        <div class="option-item">
                            <div class="option-label">
                                重试次数
                                <div class="option-description">AI 响应失败时的最大重试次数</div>
                            </div>
                            <input type="number" id="maxRetries" class="settings-input" min="0" max="5" value="${config?.maxRetries || 3}">
                        </div>
                    </div>
                </div>
            <section>
                <div class="option-group">
                    <div class="option-item">
                        <div class="main-switch-label">
                            启用会话图片识别
                            <div class="option-description">开启后将会话消息中的图片转发给AI识别</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="aiImageEnabled">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </section>
            <section>
                <div class="option-group">
                    <div class="option-item">
                        <div class="main-switch-label">
                            辅助人工回复模式
                            <div class="option-description">开启后将AI回复内容输入到聊天框，等待人工确认再发送</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="assistReplyEnabled" >
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="option-item">
                        <div class="option-label">
                            倒计时回复时间
                            <div class="option-description">等待人工确认时间，人工未确认则自动回复</div>
                        </div>
                        <input type="text" id="assistReplyTime" class="settings-input" value="30">
                    </div>
                </div>
            </section>
            <section>
                <div class="option-group">
                    <button class="save-btn" id="saveAISettings">保存设置</button>
                    <div class="save-success" id="aiSettingsSaveSuccess" style="display: none; text-align: center; margin-top: 10px;">
                        <span class="settings-saved-tip">
                             设置已保存
                        </span>
                    </div>
                </div>
            </section>
            </div>
            </div>
        `;

        const modal = createFloatingWindow('AI 设置', aiSettingsContent);

        // 设置 aiImageEnabled 复选框的状态
        const aiImageEnabledCheckbox = modal.querySelector('#aiImageEnabled');
        if (aiImageEnabledCheckbox) {
            aiImageEnabledCheckbox.checked = config.aiImageEnabled ?? false; // 根据 config.aiImageEnabled 设置选中状态，默认为 false
        }

        // 设置 assistReplyEnabled 复选框的状态
        const assistReplyEnabledCheckbox = modal.querySelector('#assistReplyEnabled');
        if (assistReplyEnabledCheckbox) {
            assistReplyEnabledCheckbox.checked = config.assistReplyEnabled ?? false; // 根据 config.aiImageEnabled 设置选中状态，默认为 false
        }

        // 添加保存按钮事件
        const saveButton = modal.querySelector('#saveAISettings');
        saveButton.addEventListener('click', () => this.saveAISettings(modal));
    }

    async saveAISettings(modal) {
        debug('[AI设置] 保存设置');
        const apiKey = document.getElementById('difyApiKey').value.trim();
        const maxResponseTime = parseInt(document.getElementById('maxResponseTime').value);
        const maxRetries = parseInt(document.getElementById('maxRetries').value);
        const assistReplyEnabled = document.getElementById('assistReplyEnabled')?.checked ?? false;
        const assistReplyTime = parseInt(document.getElementById('assistReplyTime').value);

        // 验证输入
        if ( !apiKey) {
            alert('请填写完整的 API 信息');
            return;
        }

        try {
            // 保存设置到 chrome.storage.local
            // 增加保存图片识别  
            await StateManager.setState('aiSettings', {
                apiKey,
                maxResponseTime,
                maxRetries,
                aiImageEnabled: document.getElementById('aiImageEnabled')?.checked ?? false,
                assistReplyEnabled,
                assistReplyTime
            });
            debug('[AI设置] 保存设置成功');
            // 同时更新后台脚本中的配置
            chrome.runtime.sendMessage({
                type: 'updateDifyConfig',
                data: { apiKey }
            });
            debug('[AI设置] 更新后台脚本中的配置');
            
            // 显示保存成功提示
            const successTip = modal.querySelector('#aiSettingsSaveSuccess');
            if (successTip) {
                successTip.style.display = 'block';
                successTip.style.opacity = '1';
                
                setTimeout(() => {
                    successTip.style.opacity = '0';
                    setTimeout(() => {
                        successTip.style.display = 'none';
                    }, 300);
                }, 2000);
            }
            
            debug('[AI设置] 保存设置成功22');
        } catch (error) {
            debug('[AI设置] 保存设置失败:', error);
            alert('保存设置失败，请重试');
        }
    }
}

// 创建单例实例
let floatingBallInstance = null;

// 判断消息类型
function determineMessageType(message) {
    if (!message?.element) return MESSAGE_TYPES.UNKNOWN;
    
    debug('开始判断消息类型');
    
    // 先检查商品卡
    const goodCard = message.element.querySelector('.msg-content.good-card');
    if (goodCard) {
        debug('检测到商品卡消息');
        return MESSAGE_TYPES.TEXT;  // 商品卡作为文本消息处理
    }
    
    // 再检查文本内容
    if (message.content && typeof message.content === 'string' && message.content.trim()) {
        debug('检测到文本消息');
        return MESSAGE_TYPES.TEXT;
    }
    
    // 最后检查其他类型
    if (message.element.querySelector('.order-card')) {
        debug('检测到订单消息');
        return MESSAGE_TYPES.ORDER;
    }
    // 检查两种售后卡片
    if (message.element.querySelector('.refund-card, .apply-card')) {
        debug('检测到退款/售后申请消息');
        return MESSAGE_TYPES.REFUND;
    }
    // 新增：检查commonCardTemp类型的售后卡片
    if (message.element.querySelector('.commonCardTemp') && 
        (message.element.querySelector('.commonCardTemp .title')?.textContent.includes('售后') ||
         message.element.querySelector('.commonCardTemp .text-content')?.textContent.includes('售后'))) {
        debug('检测到平台售后通知消息');
        return MESSAGE_TYPES.REFUND;
    }
    if (message.element.querySelector('.image-msg, .video-content') || message.content?.trim() === '[图片]') {
        debug('检测到图片消息');
        return MESSAGE_TYPES.IMAGE;
    }
    
    debug('未能识别消息类型，标记为未知类型');
    return MESSAGE_TYPES.UNKNOWN;
}

// 查找转移相关元素
function findTransferElements() {
    const input = document.querySelector('.chat-editor');
    const button = document.querySelector('.transfer-chat-wrap');
    return {
        input: input,
        button: button
    };
}

// 查找转移按钮
async function findTransferButton() {
    debug('[转人工] 开始查找转移按钮');
    
    // 定义所有可能的选择器
    const selectors = [
        '.transfer-chat-wrap',
        '.transfer-chat',
        '.transfer-btn',
        'span[data-v-309797d7]',
        'button:has(span:contains("转移"))',
        '.el-button:has(span:contains("转移"))',
        '[role="button"]:has(span:contains("转移"))'
    ];
    
    // 等待按钮出现
    for (let i = 0; i < 15; i++) {
        for (const selector of selectors) {
            const elements = document.querySelectorAll(selector);
            for (const element of elements) {
                // 检查元素是否可见且可点击
                if (element && 
                    element.offsetParent !== null && 
                    !element.disabled &&
                    (element.textContent?.includes('转移') || selector === '.transfer-chat-wrap' || selector === '.transfer-chat')) {
                    debug('[转人工] 找到转移按钮:', selector);
                    return element;
                }
            }
        }
        await sleep(500);
    }
    
    debug('[转人工] 未找到转移按钮');
    return null;
}

// 选择目标客服
async function selectTargetAgent(selectedAgent = null) {
    debug('[转人工] 开始查找目标客服');
    
    // 初始化匹配的转移按钮数组
    window.matchedTransferButtons = [];
    
    // 如果没有提供指定的客服账号，则从设置中获取
    if (!selectedAgent) {
    // 获取设置中指定的客服账号列表
    const settings = await StateManager.getState('transferSettings', {});
    let serviceAccounts = settings.serviceAccounts || [];
    
    // 兼容旧版本，如果没有serviceAccounts但有specifiedAgent，则使用specifiedAgent
    if (serviceAccounts.length === 0 && settings.specifiedAgent) {
        serviceAccounts = [settings.specifiedAgent.trim()];
    }
    
    // 检查设置是否生效
    debug('[转人工] 当前转接设置:', {
        是否启用自动转人工: settings.manualEnabled,
        客服账号列表: serviceAccounts.length > 0 ? serviceAccounts : '未设置',
        其他设置: {
            关键词触发: settings.keywordEnabled,
            图片触发: settings.imageEnabled,
            退款触发: settings.refundEnabled,
            投诉触发: settings.complaintEnabled
        }
    });

    if (serviceAccounts.length === 0) {
        debug('[转人工] 警告: 未设置客服账号');
        return null;
    }
    
    // 随机选择一个客服账号
    const randomIndex = Math.floor(Math.random() * serviceAccounts.length);
        selectedAgent = serviceAccounts[randomIndex];
    debug('[转人工] 随机选择客服账号:', selectedAgent);
    } else {
        debug('[转人工] 使用指定的客服账号:', selectedAgent);
    }
    
    // 等待对话框出现
    for (let i = 0; i < 15; i++) { // 增加等待时间
        // 尝试多种可能的对话框选择器
        const dialogs = document.querySelectorAll('.el-dialog.el-dialog--center.tranform-chat-dialog, .el-dialog__wrapper:not(.hide) .el-dialog, .tranform-chat-dialog');
        let dialog = null;
        
        // 查找可见的对话框
        for (const d of dialogs) {
            if (d && d.offsetParent !== null && getComputedStyle(d).display !== 'none') {
                dialog = d;
                break;
            }
        }
        
        if (dialog) {
            debug('[转人工] 找到客服对话框');
            
            // 先在搜索框中输入指定客服名称
            if (selectedAgent) {
                // 尝试多种可能的搜索框选择器
                const searchInputs = dialog.querySelectorAll('input[placeholder*="请输入"], input[type="text"], .el-input__inner');
                let searchInput = null;
                
                for (const input of searchInputs) {
                    if (input && input.offsetParent !== null) {
                        searchInput = input;
                        break;
                    }
                }
                
                if (searchInput) {
                    debug('[转人工] 在搜索框中输入指定客服:', selectedAgent);
                    // 先清空搜索框
                    searchInput.value = '';
                    searchInput.dispatchEvent(new Event('input', { bubbles: true }));
                    await sleep(300);
                    
                    // 设置新值并触发事件
                    searchInput.value = selectedAgent;
                    searchInput.dispatchEvent(new Event('input', { bubbles: true }));
                    searchInput.dispatchEvent(new Event('change', { bubbles: true }));
                    
                    // 等待搜索结果
                    await sleep(1500);
                } else {
                    debug('[转人工] 未找到搜索框');
                }
            }

            // 获取所有可用的转移按钮和对应的客服名称 - 使用多种选择器
            const transferRows = dialog.querySelectorAll('.el-table__body .el-table__row, .el-table tr, tr');
            if (transferRows.length > 0) {
                debug('[转人工] 找到可用的客服行:', transferRows.length);
                
                // 查找匹配的客服
                debug('[转人工] 正在查找指定的客服:', selectedAgent);
                
                // 遍历所有客服行，查找匹配的客服名称
                for (const kefu_row of transferRows) {
                    try {
                        // 获取客服名称，尝试多种可能的选择器
                        let agentName = null;
                        const possibleCells = [
                            kefu_row.querySelector(".el-table_1_column_1 .cell"),
                            kefu_row.querySelector(".cell"),
                            kefu_row.querySelector("td"),
                            kefu_row.querySelector("div")
                        ];
                        
                        for (const cell of possibleCells) {
                            if (cell && cell.textContent) {
                                agentName = cell.textContent.trim();
                                if (agentName) break;
                            }
                        }
                        
                        if (!agentName) {
                            agentName = kefu_row.textContent.trim();
                        }
                        
                        const matchResult = agentName && selectedAgent && agentName.includes(selectedAgent);
                        debug('[转人工] 正在匹配客服:', {
                            指定客服: selectedAgent,
                            当前客服: agentName,
                            匹配结果: matchResult
                        });
                        
                        if (matchResult) {
                            debug('[转人工] 找到指定的客服:', agentName);
                            
                            // 查找该行中的转移按钮
                            const transferBtn = kefu_row.querySelector('.item-btn-transfer:not(.disabled), button, [role="button"], .el-button');
                            if (transferBtn) {
                                debug('[转人工] 找到客服行中的转移按钮');
                                // 不立即返回，而是收集起来，等找完所有匹配的再随机选择一个
                                if (!window.matchedTransferButtons) {
                                    window.matchedTransferButtons = [];
                                }
                                window.matchedTransferButtons.push({
                                    button: transferBtn,
                                    agentName: agentName
                                });
                            } else {
                                debug('[转人工] 在客服行中未找到转移按钮，尝试使用整行作为点击目标');
                                if (!window.matchedTransferButtons) {
                                    window.matchedTransferButtons = [];
                                }
                                window.matchedTransferButtons.push({
                                    button: kefu_row,
                                    agentName: agentName
                                });
                            }
                        }
                    } catch (error) {
                        debug('[转人工] 处理客服行时出错:', error);
                    }
                }
                debug('[转人工] 未找到完全匹配的客服:', selectedAgent);
                
                // 检查是否已经收集到了匹配的客服按钮
                if (window.matchedTransferButtons && window.matchedTransferButtons.length > 0) {
                    // 随机选择一个匹配的客服
                    const randomIndex = Math.floor(Math.random() * window.matchedTransferButtons.length);
                    const selectedButton = window.matchedTransferButtons[randomIndex];
                    
                    debug('[转人工] 从', window.matchedTransferButtons.length, '个匹配的客服中随机选择了:', selectedButton.agentName);
                    
                    // 清空收集的按钮，以便下次使用
                    const result = selectedButton.button;
                    window.matchedTransferButtons = [];
                    return result;
                }
                
                // 如果找不到指定客服，但有可用的转移按钮，收集所有可用的转移按钮
                const availableButtons = [];
                
                for (const row of transferRows) {
                    const btn = row.querySelector('.item-btn-transfer:not(.disabled), button, [role="button"], .el-button');
                    if (btn) {
                        // 尝试获取客服名称
                        let agentName = null;
                        const possibleCells = [
                            row.querySelector(".el-table_1_column_1 .cell"),
                            row.querySelector(".cell"),
                            row.querySelector("td"),
                            row.querySelector("div")
                        ];
                        
                        for (const cell of possibleCells) {
                            if (cell && cell.textContent) {
                                agentName = cell.textContent.trim();
                                if (agentName) break;
                            }
                        }
                        
                        if (!agentName) {
                            agentName = row.textContent.trim();
                        }
                        
                        availableButtons.push({
                            button: btn,
                            agentName: agentName
                        });
                    }
                }
                
                if (availableButtons.length > 0) {
                    // 随机选择一个可用的客服
                    const randomIndex = Math.floor(Math.random() * availableButtons.length);
                    const selectedButton = availableButtons[randomIndex];
                    
                    debug('[转人工] 未找到指定客服，从', availableButtons.length, '个可用客服中随机选择了:', selectedButton.agentName);
                    return selectedButton.button;
                } else {
                    debug('[转人工] 未找到任何可用的转移按钮');
                    return null;
                }
            } else {
                debug('[转人工] 未找到任何客服行');
            }
        }
        await sleep(500);
    }
    
    debug('[转人工] 未找到可用的客服');
    return null;
}

// 查找确认按钮
async function findConfirmButton() {
    const buttons = document.querySelectorAll('.item-btn-transfer');
    if (buttons.length === 0) {
        console.log('[转人工] 未找到确认按钮');
        return null;
    }
    return buttons[0];
}

// 查找转移原因按钮
async function findTransferReasonButton() {
    debug('[转人工] 开始查找转移原因按钮');
    
    // 等待按钮出现
    for (let i = 0; i < 15; i++) {
        // 查找转移原因列表容器
        const remarkBoxs = document.querySelectorAll('.transform-list-popover');
        let doRemarkBox;
        for (const remarkBox of remarkBoxs) {
            const style = getComputedStyle(remarkBox);
              if (style.display !== 'none') {
                doRemarkBox = remarkBox;
                            break;
            }
        }
        
        if (doRemarkBox) {
            // 查找"无原因直接转移"选项
            const items = doRemarkBox.querySelectorAll('.trasnfer-remark-item');
            for (const item of items) {
                const reasonContent = item.querySelector('.reason-content');
                if (reasonContent && reasonContent.textContent && reasonContent.textContent.trim() === '无原因直接转移') {
                        debug('[转人工] 找到"无原因直接转移"选项');
                    return reasonContent;
                }
            }
            
            // 备用方案：如果找不到特定的"无原因直接转移"，查找任何包含"转移"的元素
            const transferItems = doRemarkBox.querySelectorAll('div, span, button');
            for (const item of transferItems) {
                if (item.textContent && item.textContent.trim().includes('转移')) {
                    debug('[转人工] 找到包含"转移"的元素:', item.textContent.trim());
                    return item;
                }
            }
        }
        
        // 如果上面的方法都失败了，尝试查找带有data-v属性的span
        const spans = document.querySelectorAll('span[data-v-309797d7]');
        for (const span of spans) {
            if (span.textContent?.trim() === '转移') {
                const clickableParent = span.closest('button') || span.closest('[role="button"]') || span.closest('.el-button') || span;
                debug('[转人工] 找到转移按钮 (通过 data-v 属性)');
                return clickableParent;
            }
        }
        
        await sleep(500);
    }
    
    debug('[转人工] 未找到转移原因按钮');
    return null;
}

// 查找"取消收藏并转移"按钮或处理意见反馈对话框
async function findCancelStarButton() {
    debug('[转人工] 开始查找"取消收藏并转移"按钮或处理意见反馈对话框');
    
    // 等待按钮出现
    for (let i = 0; i < 15; i++) {
        try {
            // 查找所有可能的对话框
            const dialogs = document.querySelectorAll('.el-dialog__wrapper:not(.hide), .el-dialog, .el-message-box, .el-popover--plain, .el-popper');
            
            for (const dialog of dialogs) {
                if (!dialog || dialog.offsetParent === null || getComputedStyle(dialog).display === 'none') {
                    continue; // 跳过不可见的对话框
                }
                
                debug('[转人工] 找到可见对话框:', dialog.className);
                
                // 记录对话框内容以便调试
                const dialogContent = dialog.textContent.trim();
                debug('[转人工] 对话框内容:', dialogContent);
                
                // 检查是否是意见反馈对话框
                if (dialogContent.includes('意见反馈')) {
                    debug('[转人工] 检测到意见反馈对话框');
                    
                    // 查找表单输入区域并填写内容
                    const textarea = dialog.querySelector('textarea, .el-textarea__inner, input[type="text"]');
                    if (textarea) {
                        debug('[转人工] 找到意见反馈输入框，填写内容');
                        textarea.value = '客户要求转人工客服';
                        textarea.dispatchEvent(new Event('input', { bubbles: true }));
                        textarea.dispatchEvent(new Event('change', { bubbles: true }));
                        await sleep(300);
                    }
                    
                    // 查找提交按钮
                    const buttons = dialog.querySelectorAll('button, .el-button');
                    for (const button of buttons) {
                        const btnText = button.textContent.trim();
                        if (btnText.includes('提交') || btnText.includes('提 交')) {
                            debug('[转人工] 在意见反馈对话框中找到"提交"按钮');
                            return button;
                        }
                    }
                    
                    // 如果没有找到明确的提交按钮，尝试查找主要按钮
                    const primaryButton = dialog.querySelector('.el-button--primary');
                    if (primaryButton) {
                        debug('[转人工] 在意见反馈对话框中找到主要按钮:', primaryButton.textContent);
                        return primaryButton;
                    }
                }
                
                // 方法1：查找"取消收藏并转移"按钮
                const buttons = dialog.querySelectorAll('.el-button, button, [role="button"]');
                for (const button of buttons) {
                    if (button.textContent && button.textContent.trim().includes('取消收藏并转移')) {
                        debug('[转人工] 找到"取消收藏并转移"按钮 (遍历方法)');
                        return button;
                    }
                }
                
                // 方法2：尝试通过XPath查找
                try {
                    const xpaths = [
                        "//button[contains(text(), '取消收藏并转移')]",
                        "//span[contains(text(), '取消收藏并转移')]/ancestor::button",
                        "//button[contains(text(), '取消收藏')]",
                        "//button[contains(text(), '转移')]"
                    ];
                    
                    for (const xpath of xpaths) {
                        const result = document.evaluate(xpath, dialog, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                        if (result.singleNodeValue) {
                            debug('[转人工] 通过XPath找到按钮:', result.singleNodeValue.textContent);
                            return result.singleNodeValue;
                        }
                    }
                } catch (xpathError) {
                    debug('[转人工] XPath查找出错:', xpathError);
                }
                
                // 方法3：尝试查找主要按钮
                const primaryButtons = dialog.querySelectorAll('.el-button--primary');
                for (const button of primaryButtons) {
                    if (button.textContent) {
                        const btnText = button.textContent.trim();
                        if (btnText.includes('取消收藏') || btnText.includes('转移') || btnText.includes('提交') || btnText.includes('确定')) {
                            debug('[转人工] 找到可能的操作按钮 (主要按钮):', btnText);
                            return button;
                        }
                    }
                }
                
                // 方法4：查找任何可能的确认按钮
                for (const button of buttons) {
                    if (button.textContent) {
                        const btnText = button.textContent.trim();
                        if (btnText.includes('确定') || btnText.includes('提交') || btnText.includes('是') || 
                            btnText.includes('转移') || btnText.includes('转接')) {
                            debug('[转人工] 找到可能的确认按钮:', btnText);
                            return button;
                        }
                    }
                }
                
                // 记录对话框中所有按钮的文本内容
                const allButtons = dialog.querySelectorAll('button, .el-button');
                const buttonTexts = Array.from(allButtons).map(btn => btn.textContent.trim());
                debug('[转人工] 对话框中的所有按钮:', buttonTexts);
                
                // 如果有按钮但没有找到匹配的，返回第一个非"取消"按钮
                for (const button of buttons) {
                    const btnText = button.textContent.trim();
                    if (btnText && !btnText.includes('取消') && !btnText.includes('关闭')) {
                        debug('[转人工] 使用第一个非取消按钮:', btnText);
                        return button;
                    }
                }
                
                // 如果只找到一个按钮，无论是什么都返回
                if (buttons.length === 1) {
                    debug('[转人工] 只找到一个按钮，直接使用:', buttons[0].textContent);
                    return buttons[0];
                }
            }
        } catch (error) {
            debug('[转人工] 查找按钮时出错:', error);
        }
        await sleep(300); // 减少等待时间，更快地检查
    }
    
    debug('[转人工] 未找到"取消收藏并转移"按钮或其他可操作按钮');
    return null;
}

// 工具函数：休眠
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 发送消息
async function sendMessage(text) {
    return await sendToPddCustomer(text);

    //const input = document.querySelector('.chat-editor');
    // const input = document.querySelector('#replyTextarea');
    // if (!input) {
    //     console.log('[转人工] 未找到输入框');
    //     return false;
    // }

    // // 设置输入框的值
    // input.value = text;
    // input.dispatchEvent(new Event('input'));

    // //读取配置是否要辅助人工回复
    // let config = await StateManager.getState('aiSettings', {});
    // const assistReplyEnabled = config?.assistReplyEnabled;
    // if(assistReplyEnabled.assistReplyEnabled) {
    //     //倒计时发送
    
    // }else {
    //     // 模拟按下回车键
    //     const enterEvent = new KeyboardEvent('keydown', {
    //         key: 'Enter',
    //         code: 'Enter',
    //         keyCode: 13,
    //         bubbles: true
    //     });
    //     input.dispatchEvent(enterEvent);
    // }
    

    // // 等待消息发送
    // return new Promise(resolve => {
    //     const checkInterval = setInterval(() => {
    //         if (input.value === '') {
    //             clearInterval(checkInterval);
    //             resolve(true);
    //         }
    //     }, 100);

    //     // 5秒超时
    //     setTimeout(() => {
    //         clearInterval(checkInterval);
    //         resolve(false);
    //     }, 5000);
    // });
}

// 创建浮窗
function createFloatingWindow(title, content) {
    const modal = document.createElement('div');
    modal.className = 'floating-window-overlay';
    
    const window = document.createElement('div');
    window.className = 'floating-window';
    
    window.innerHTML = `
        <div class="floating-window-header">
            <h2>${title}</h2>
            <button class="close-button">×</button>
        </div>
        <div class="floating-window-content">
            ${content}
        </div>
    `;
    
    modal.appendChild(window);
    document.body.appendChild(modal);
    
    // 添加关闭事件
    const closeButton = window.querySelector('.close-button');
    closeButton.onclick = () => {
        modal.remove();
    };
    
    // 点击遮罩层关闭
    modal.onclick = (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    };
    
    return modal;
}

// 显示其他设置浮窗（添加备注）
function showOtherSettingsWindow() {
    const otherSettingsContent = `
        <div class="other-settings-container">
            <div class="section">
                <div class="option-group">
                    <div class="option-item">
                        <div class="main-switch-label">
                            订单自动添加备注
                            <div class="option-description">AI根据知识库条件来判断或下方添加关键词触发</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="backupKeywordEnabled" >
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <div class="option-item keyword-management">
                        <div class="keyword-input-group">
                            <input type="text" id="newBackupKeyword" placeholder="输入触发订单备注关键词">
                            <button id="addBackupKeyword" class="keyword-btn">添加</button>
                        </div>
                        <div id="backupKeywordList" class="keyword-list"></div>
                    </div>
                                        <div class="option-item keyword-management">
                        <div class="prompt-label">
                            订单备注失败提示语
                        </div>
                        <div class="keyword-input-group">
                            <input type="text" id="orderNoteReplyMessage" placeholder="请输入提示语">
                            <button id="orderNoteReplyMessageSave" class="keyword-btn">修改</button>
                        </div>
                        <div id="orderNoteReplyMessageSaveSuccess" style="display: none; text-align: center; margin-top: 10px;">
                            <span class="settings-saved-tip">
                                <span>✓</span> 设置已保存
                            </span>
                        </div>
                    </div>
                </div>
                <div class="option-group">
                    <div class="option-item">
                        <div class="main-switch-label">
                            用户自动标记星标
                            <div class="option-description">AI根据知识库条件来判断或下方添加关键词触发</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="starFlagKeywordEnabled" >
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div class="option-item keyword-management">
                        <div class="keyword-input-group">
                            <input type="text" id="newStarFlagKeyword" placeholder="输入触发打星标关键词">
                            <button id="addStarFlagKeyword" class="keyword-btn">添加</button>
                        </div>
                        <div id="starFlagKeywordList" class="keyword-list"></div>
                    </div>
                    <div class="option-item keyword-management">
                        <div class="prompt-label">
                            标记星标失败提示语
                        </div>
                        <div class="keyword-input-group">
                            <input type="text" id="starFlagReplyMessage" placeholder="请输入提示语">
                            <button id="starFlagReplyMessageSave" class="keyword-btn">修改</button>
                        </div>
                        <div id="starFlagReplyMessageSaveSuccess" style="display: none; text-align: center; margin-top: 10px;">
                            <span class="settings-saved-tip">
                                <span>✓</span> 设置已保存
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="option-group">
                    <div class="option-item">
                        <div class="main-switch-label">
                            卡片消息自定义回复
                            <div class="option-description">当用户发送商品卡片消息时可自定义回复内容</div>
                        </div>
                    </div>
                    <div class="option-item keyword-management">
                        <div class="keyword-input-group">
                            <input type="text" id="customProductReply" placeholder="请输入回复内容">
                            <button id="customProductReplySave" class="keyword-btn">修改</button>
                        </div>
                        <div id="customProductReplySaveSuccess" style="display: none; text-align: center; margin-top: 10px;">
                            <span class="settings-saved-tip">
                                <span>✓</span> 设置已保存
                            </span>
                        </div>
                    </div>
                </div>
                <div class="option-group">
                    <div class="option-item">
                        <div class="main-switch-label">
                            自动同意修改地址
                            <div class="option-description">开启后待发货订单将自动同意修改地址</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="modifyAddressEnabled" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div class="option-item keyword-management">
                        <div class="prompt-label">
                            不开启时提示语
                        </div>
                        <div class="keyword-input-group">
                            <input type="text" id="modifyAddressDisabledMessage" placeholder="请输入不支持改地址提示语">
                            <button id="modifyAddressDisabledMessageSave" class="keyword-btn">修改</button>
                        </div>
                        <div id="modifyAddressDisabledMessageSaveSuccess" style="display: none; text-align: center; margin-top: 10px;">
                            <span class="settings-saved-tip">
                                <span>✓</span> 设置已保存
                            </span>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    `;


    const modal = createFloatingWindow('其他设置', otherSettingsContent);

    const addKeywordButton = modal.querySelector('#addBackupKeyword');
    addKeywordButton.onclick = addBackupKeyword;

    // 备注关键词开关保存
    const backupKeywordEnabledButton = modal.querySelector('#backupKeywordEnabled');
    backupKeywordEnabledButton.onclick = saveOtherSettings;

    // 加载设置
    chrome.storage.local.get('backupKeywordEnabled').then(res=> {
        backupKeywordEnabledButton.checked = res.backupKeywordEnabled;
    }).catch(error => {
        backupKeywordEnabledButton.checked = false;
    }) ;
    
    loadBackupKeywordList(); // 重新加载关键词列表


    const addStarFlagKeywordButton = modal.querySelector('#addStarFlagKeyword');
    addStarFlagKeywordButton.onclick = addStarFlagKeyword;

    // 备注关键词开关保存
    const starFlagKeywordEnabledButton = modal.querySelector('#starFlagKeywordEnabled');
    starFlagKeywordEnabledButton.onclick = saveOtherSettings;

    // 自定义回复文案
    const customProductReplySaveButton = modal.querySelector('#customProductReplySave');
    customProductReplySaveButton.onclick = saveOtherSettings;
    
    // 订单备注提示语保存按钮事件
    const orderNoteReplyMessageSaveButton = modal.querySelector('#orderNoteReplyMessageSave');
    if (orderNoteReplyMessageSaveButton) {
        orderNoteReplyMessageSaveButton.onclick = saveOtherSettings;
    }
    
    // 标记星标提示语保存按钮事件
    const starFlagReplyMessageSaveButton = modal.querySelector('#starFlagReplyMessageSave');
    if (starFlagReplyMessageSaveButton) {
        starFlagReplyMessageSaveButton.onclick = saveOtherSettings;
    }

    // 自动同意修改地址
    const modifyAddressEnabledButton = modal.querySelector('#modifyAddressEnabled');
    modifyAddressEnabledButton.onclick = saveOtherSettings;
    
    // 不开启自动同意改地址提示语
    const modifyAddressDisabledMessageSaveButton = modal.querySelector('#modifyAddressDisabledMessageSave');
    if (modifyAddressDisabledMessageSaveButton) {
        modifyAddressDisabledMessageSaveButton.onclick = saveOtherSettings;
    }
    
    // 加载不开启自动同意改地址提示语
    chrome.storage.local.get('modifyAddressDisabledMessage').then(res => {
        const message = res.modifyAddressDisabledMessage || '抱歉不支持改地址哦，建议重新拍下';
        const messageInput = document.querySelector('#modifyAddressDisabledMessage');
        if (messageInput) {
            messageInput.value = message;
            debug('[改地址] 加载不开启自动同意改地址提示语:', message);
        }
    }).catch(error => {
        const messageInput = document.querySelector('#modifyAddressDisabledMessage');
        if (messageInput) {
            messageInput.value = '抱歉不支持改地址哦，建议重新拍下';
            debug('[改地址] 加载不开启自动同意改地址提示语失败，使用默认值:', error);
        }
    });
    
    // 加载设置
    chrome.storage.local.get('starFlagKeywordEnabled').then(res=> {
        starFlagKeywordEnabledButton.checked = res.starFlagKeywordEnabled;
    }).catch(error => {
        starFlagKeywordEnabledButton.checked = false;
    }) ;
    
    loadStarFlagKeywordList(); // 重新加载关键词列表

    // 加载自定义回复文案
    chrome.storage.local.get('customProductReply').then(res=> {
        if(res.customProductReply) {
            document.querySelector('#customProductReply').value = res.customProductReply;
        } else {
            // 设置默认值
            const customProductReplyInput = document.querySelector('#customProductReply');
            if (customProductReplyInput) {
                customProductReplyInput.value = '这款商品有什么需要了解吗';
                // 保存默认值
                chrome.storage.local.set({ customProductReply: '这款商品有什么需要了解吗' });
            }
        }
    }).catch(error => {
        // 出错时设置默认值
        const customProductReplyInput = document.querySelector('#customProductReply');
        if (customProductReplyInput) {
            customProductReplyInput.value = '这款商品有什么需要了解吗';
        }
    }) ;
    
    // 加载订单备注提示语
    chrome.storage.local.get('orderNoteReplyMessage').then(res => {
        if (res.orderNoteReplyMessage) {
            const orderNoteReplyMessageInput = document.querySelector('#orderNoteReplyMessage');
            if (orderNoteReplyMessageInput) {
                orderNoteReplyMessageInput.value = res.orderNoteReplyMessage;
            }
        } else {
            // 设置默认值
            const orderNoteReplyMessageInput = document.querySelector('#orderNoteReplyMessage');
            if (orderNoteReplyMessageInput) {
                orderNoteReplyMessageInput.value = '收到，稍后为您处理';
                // 保存默认值
                chrome.storage.local.set({ orderNoteReplyMessage: '收到，稍后为您处理' });
            }
        }
    }).catch(error => {
        // 出错时设置默认值
        const orderNoteReplyMessageInput = document.querySelector('#orderNoteReplyMessage');
        if (orderNoteReplyMessageInput) {
            orderNoteReplyMessageInput.value = '收到，稍后为您处理';
        }
    });
    
    // 加载标记星标提示语
    chrome.storage.local.get('starFlagReplyMessage').then(res => {
        if (res.starFlagReplyMessage) {
            const starFlagReplyMessageInput = document.querySelector('#starFlagReplyMessage');
            if (starFlagReplyMessageInput) {
                starFlagReplyMessageInput.value = res.starFlagReplyMessage;
            }
        } else {
            // 设置默认值
            const starFlagReplyMessageInput = document.querySelector('#starFlagReplyMessage');
            if (starFlagReplyMessageInput) {
                starFlagReplyMessageInput.value = '收到，稍后为您处理';
                // 保存默认值
                chrome.storage.local.set({ starFlagReplyMessage: '收到，稍后为您处理' });
            }
        }
    }).catch(error => {
        // 出错时设置默认值
        const starFlagReplyMessageInput = document.querySelector('#starFlagReplyMessage');
        if (starFlagReplyMessageInput) {
            starFlagReplyMessageInput.value = '收到，稍后为您处理';
        }
    });

    // 加载自动同意修改地址
    chrome.storage.local.get('modifyAddressEnabled').then(res=> {
        // 如果设置存在，使用存储的值，否则默认为true
        modifyAddressEnabledButton.checked = res.modifyAddressEnabled !== undefined ? res.modifyAddressEnabled : true;
    }).catch(error => {
        // 出错时默认为开启状态
        modifyAddressEnabledButton.checked = true;
    }) ;
}
//添加备注的关键词管理触发添加事件
async function addBackupKeyword() {
    let newkeyword = document.getElementById('newBackupKeyword').value;
    if (!newkeyword || typeof newkeyword !== 'string') {
        return false;
    }

    newkeyword = newkeyword.trim();
    if (newkeyword.length === 0) {
        return false;
    }

    //检查重复
    const keywords = await loadBackupKeywords();
    if (keywords.includes(newkeyword)) {
        return false;
    }

    keywords.push(newkeyword);
    await saveBackupKeywords(keywords);
    //重置输入框
    document.getElementById('newBackupKeyword').value = "";
    await loadBackupKeywordList(); // 重新加载关键词列表
    return true;
}
//删除备注关键词
async function removeBackupKeyword(keyword) {
    const keywords = await loadBackupKeywords();
    const index = keywords.indexOf(keyword);
    if (index > -1) {
        keywords.splice(index, 1);
        await saveBackupKeywords(keywords);
        return true;
    }
    return false;
}
// 关键词管理
async function loadBackupKeywords() {
    return chrome.storage.local.get('backupKeywords').then(result => result.backupKeywords || []);
}
async function saveBackupKeywords(keywords) {
    return chrome.storage.local.set({ backupKeywords: keywords });
}

// 加载关键词列表
async function loadBackupKeywordList() {
    const keywordList = document.getElementById('backupKeywordList');
    if (!keywordList) return;

    const keywords = await loadBackupKeywords();
    
    keywordList.innerHTML = keywords.map(keyword => `
        <div class="keyword-item">
            <span>${keyword}</span>
            <button class="delete-keyword" data-keyword="${keyword}">×</button>
        </div>
    `).join('');

    // 添加删除事件监听
    keywordList.querySelectorAll('.delete-keyword').forEach(button => {
        button.onclick = async () => {
            const keyword = button.dataset.keyword;
            if (keyword) {
                const success = await removeBackupKeyword(keyword);
                if (success) {
                    loadBackupKeywordList(); // 重新加载关键词列表
                }
            }
        };
    });
}
async function saveOtherSettings() {

    //获取点击得当前元素
    if(this.id == 'backupKeywordEnabled'){
        //保存备注关键词开关
        chrome.storage.local.set({ backupKeywordEnabled: (this?.checked ?? false) });
    }else if(this.id == 'starFlagKeywordEnabled') {
        //保存星标关键词开关
        chrome.storage.local.set({ starFlagKeywordEnabled: (this?.checked ?? false) });
    }else if(this.id == 'customProductReplySave') {
        //保存自定义回复文案
        chrome.storage.local.set({ customProductReply: (document.querySelector('#customProductReply').value ?? '') });
        const successTip = document.querySelector('#customProductReplySaveSuccess');
        if (successTip) {
            successTip.style.display = 'block';
            successTip.style.opacity = '1';
            
            setTimeout(() => {
                successTip.style.opacity = '0';
                setTimeout(() => {
                    successTip.style.display = 'none';
                }, 300);
            }, 2000);
        }
    }else if(this.id == 'modifyAddressEnabled') {
        //保存自动同意修改地址
        chrome.storage.local.set({ modifyAddressEnabled: (this?.checked ?? false) });
    }else if(this.id == 'modifyAddressDisabledMessageSave') {
        //保存不开启自动同意改地址提示语
        chrome.storage.local.set({ modifyAddressDisabledMessage: (document.querySelector('#modifyAddressDisabledMessage').value ?? '') });
        const successTip = document.querySelector('#modifyAddressDisabledMessageSaveSuccess');
        if (successTip) {
            successTip.style.display = 'block';
            successTip.style.opacity = '1';
            
            setTimeout(() => {
                successTip.style.opacity = '0';
                setTimeout(() => {
                    successTip.style.display = 'none';
                }, 300);
            }, 2000);
        }
        debug('[改地址] 保存不开启自动同意改地址提示语:', document.querySelector('#modifyAddressDisabledMessage').value);
    }else if(this.id == 'orderNoteReplyMessageSave') {
        //保存订单备注提示语
        chrome.storage.local.set({ orderNoteReplyMessage: (document.querySelector('#orderNoteReplyMessage').value ?? '') });
        const successTip = document.querySelector('#orderNoteReplyMessageSaveSuccess');
        if (successTip) {
            successTip.style.display = 'block';
            successTip.style.opacity = '1';
            
            setTimeout(() => {
                successTip.style.opacity = '0';
                setTimeout(() => {
                    successTip.style.display = 'none';
                }, 300);
            }, 2000);
        }
    }else if(this.id == 'starFlagReplyMessageSave') {
        //保存标记星标提示语
        chrome.storage.local.set({ starFlagReplyMessage: (document.querySelector('#starFlagReplyMessage').value ?? '') });
        const successTip = document.querySelector('#starFlagReplyMessageSaveSuccess');
        if (successTip) {
            successTip.style.display = 'block';
            successTip.style.opacity = '1';
            
            setTimeout(() => {
                successTip.style.opacity = '0';
                setTimeout(() => {
                    successTip.style.display = 'none';
                }, 300);
            }, 2000);
        }
    }
    
}
//检查是否触发加备注动作
async function checkBackupKeywordEnabled() {
    //检查开关
    let result = await chrome.storage.local.get('backupKeywordEnabled');
    if(result.backupKeywordEnabled) {
        return true;
    }
    return false;
}
//检查是否触发加备注动作
async function checkBackupKeyword(msg) {
    
    //检查关键词
    const keywords = await loadBackupKeywords();
    if (msg && keywords.some(keyword => msg.includes(keyword))) {
        return true;
        
    }

    return false;
}


//调试页面结构的辅助函数
function debugPageStructure() {
    debug('[页面结构] 开始调试页面结构');

    // 检查标签栏（根据实际页面结构）
    const barBox = document.querySelector('.bar-box.four-tab');
    debug('[页面结构] 标签栏存在:', !!barBox);

    if (barBox) {
        debug('[页面结构] 标签栏类名:', barBox.className);
        const barItems = barBox.querySelectorAll('.bar-item');
        debug('[页面结构] 标签项数量:', barItems.length);

        barItems.forEach((item, index) => {
            debug(`[页面结构] 标签${index}:`, item.textContent.trim());
        });
    }

    // 检查订单二级面板
    const orderPanelBars = document.querySelectorAll('.order-panel-second-bar');
    debug('[页面结构] 订单二级面板数量:', orderPanelBars.length);

    orderPanelBars.forEach((panel, index) => {
        debug(`[页面结构] 二级面板${index}:`, panel.textContent.trim());
    });

    // 检查订单列表
    const orderList = document.querySelector('.order-item-list');
    debug('[页面结构] 订单列表存在:', !!orderList);

    if (orderList) {
        const orderItems = orderList.querySelectorAll('.order-item');
        debug('[页面结构] 订单项数量:', orderItems.length);

        if (orderItems.length > 0) {
            const firstOrder = orderItems[0];
            debug('[页面结构] 第一个订单信息:');
            debug('  - 订单状态:', firstOrder.querySelector('.title-status')?.textContent.trim() || '未找到');
            debug('  - 订单编号:', firstOrder.querySelector('.order-sn')?.textContent.trim() || '未找到');
            debug('  - 商品数量:', firstOrder.querySelector('.goods-num')?.textContent.trim() || '未找到');
            debug('  - 实收金额:', firstOrder.querySelector('.amount-value')?.textContent.trim() || '未找到');
        }
    }
}

//查找按钮
async function findBackupKeywordOrderItem() {
    debug('[查找订单] 开始查找最新订单');

    // 先调试页面结构
    debugPageStructure();

    // 根据实际页面结构修正选择器
    let bar_items = document.querySelectorAll('.bar-box.four-tab .bar-item');
    debug('[查找订单] 找到标签数量:', bar_items.length);

    // 如果没找到，尝试其他选择器
    if (bar_items.length === 0) {
        debug('[查找订单] 尝试其他选择器');
        bar_items = document.querySelectorAll('.bar-box .bar-item');
        debug('[查找订单] 使用备用选择器找到标签数量:', bar_items.length);
    }

    for (bar_item_tab of bar_items) {
        const tabText = bar_item_tab.textContent.trim();
        debug('[查找订单] 检查标签:', `"${tabText}"`);
        if(tabText === '最新订单') {
            debug('[查找订单] 找到最新订单标签，开始点击');
            //模拟点击最新订单
            bar_item_tab.click();

            // 等待一下让页面响应
            await new Promise(resolve => setTimeout(resolve, 500));

            if (await waitForElementOrTimeout('.order-panel-second-bar', 5000)) {
                debug('[查找订单] 成功等待到二级标签栏');
                // 执行你的回调函数
                let order_panel_second_bar = document.querySelectorAll('.order-panel-second-bar');
                debug('[查找订单] 找到二级标签数量:', order_panel_second_bar.length);

                for (let i = 0; i < order_panel_second_bar.length; i++) {
                    const secondTabText = order_panel_second_bar[i].textContent.trim();
                    debug('[查找订单] 检查二级标签:', `"${secondTabText}"`);
                }

                if(order_panel_second_bar[0] && order_panel_second_bar[0].textContent.trim() === '个人订单') {
                    debug('[查找订单] 找到个人订单标签，开始点击');
                    order_panel_second_bar[0].click();

                    // 等待一下让页面响应
                    await new Promise(resolve => setTimeout(resolve, 500));

                    if (await waitForElementOrTimeout('.order-item-list .order-item', 5000)) {
                        debug('[查找订单] 成功等待到订单列表');
                        //个人订单面板
                        let order_items = document.querySelectorAll('.order-item-list .order-item');
                        debug('[查找订单] 找到订单项数量:', order_items.length);

                        if(order_items.length > 0) {
                            //获取订单状态
                            let first_order_item = order_items[0];
                            debug('[查找订单] 成功获取第一个订单项');
                            return first_order_item;
                        } else {
                            debug('[查找订单] 订单列表为空');
                        }

                        return null;
                    } else {
                        debug('[查找订单] 等待订单列表超时，尝试直接查找现有订单项');
                        // 即使超时也尝试查找现有的订单项
                        let order_items = document.querySelectorAll('.order-item-list .order-item');
                        debug('[查找订单] 超时后找到订单项数量:', order_items.length);

                        if(order_items.length > 0) {
                            let first_order_item = order_items[0];
                            debug('[查找订单] 超时后成功获取第一个订单项');
                            return first_order_item;
                        }
                    }
                } else {
                    debug('[查找订单] 未找到个人订单标签或标签文本不匹配');
                }
            } else {
                debug('[查找订单] 等待二级标签栏超时');
            }

        }
    }

    debug('[查找订单] 未找到最新订单标签');
    return null;
}
//添加备注
async function actionBackupKeyword(c, order_item) {
    debug('[debug] 输入备注内容:', c);
    //获取备注输入框
    let btn_items = order_item.querySelectorAll('.order-btn .order-btn-item');

    if(btn_items.length > 0 ) {
        for(let i = 0; i < btn_items.length; i++) {
            let btn_item = btn_items[i];
            
            if(btn_item.textContent.includes('添加备注') || btn_item.textContent.includes('修改备注')) {
                debug('[debug] 按钮item:', btn_item);
                // 判断是添加备注还是修改备注
                const isModifyRemark = btn_item.textContent.includes('修改备注');
                
                //btn_item.click();
                if(typeof btn_item.scrollIntoView === 'function'){
                    btn_item.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                await sleep(500);
                let rect = btn_item.getBoundingClientRect();
                let clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window,
                    clientX: rect.left + rect.width / 2,
                    clientY: rect.top + rect.height / 2
                });
                btn_item.dispatchEvent(clickEvent);
                await sleep(1000);
                if (await waitForElementOrTimeout('.remark-box .remark-edit .order-remark-input textarea', 5000)) {

                    let remark_input_textarea = order_item.querySelector('.remark-box .remark-edit .order-remark-input textarea');
                    if(remark_input_textarea) {
                        remark_input_textarea.focus(); // 获取焦点
                        
                        // 获取原始备注内容
                        const originalRemarkContent = remark_input_textarea.value.trim();
                        debug('[debug] 原始备注内容:', originalRemarkContent);
                        
                        // 检查新内容是否已包含在原始内容中
                        if (isModifyRemark && (originalRemarkContent.includes(c) || originalRemarkContent + ' ' + c === originalRemarkContent)) {
                            debug('[debug] 备注内容重复，跳过添加');
                            // 内容重复，关闭备注框而不保存
                            let remark_cancel_btn = order_item.querySelector('.remark-box .remark-edit .btn-box .cancel-btn');
                            if (remark_cancel_btn) {
                                remark_cancel_btn.click();
                                // 发送默认回复文案
                                sendMessage('好的，已经备注了 ' + getEmojiByType('order'));
                                return true;
                            }
                        } else {
                            // 内容不重复或是添加备注，正常执行添加操作
                            // 获取原生 textarea 元素的 value 属性的 setter 方法
                            let nativeTextAreaValueSetter = Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype, "value").set;

                            // 调用 setter 方法，手动更新 value 属性
                            nativeTextAreaValueSetter.call(remark_input_textarea, remark_input_textarea.value + ' ' + c);

                            // 派发 input 事件
                            let inputEvent = new Event('input', {
                                bubbles: true,
                                cancelable: true
                            });
                            remark_input_textarea.dispatchEvent(inputEvent);

                            // 派发 change 事件
                            let changeEvent = new Event('change', {
                                bubbles: true,
                                cancelable: true
                            });
                            remark_input_textarea.dispatchEvent(changeEvent);


                            await sleep(500);
                            debug('[debug] 备注输入框内容remark_input_textarea:', remark_input_textarea.value);
                            //保存
                            let remark_save_btn = order_item.querySelector('.remark-box .remark-edit .btn-box .save-btn');
                            if(remark_save_btn) {
                                remark_save_btn.click();
                                return true;
                            }
                        }
                    }
                    
                }
            }
        }
    }

    return false;
}

//星标关键词管理触发添加事件
async function addStarFlagKeyword() {
    let newkeyword = document.getElementById('newStarFlagKeyword').value;
    if (!newkeyword || typeof newkeyword !== 'string') {
        return false;
    }

    newkeyword = newkeyword.trim();
    if (newkeyword.length === 0) {
        return false;
    }

    //检查重复
    const keywords = await loadStarFlagKeywords();
    if (keywords.includes(newkeyword)) {
        return false;
    }

    keywords.push(newkeyword);
    await saveStarFlagKeywords(keywords);
    //重置输入框
    document.getElementById('newStarFlagKeyword').value = "";
    await loadStarFlagKeywordList(); // 重新加载关键词列表
    return true;
}
//删除备注关键词
async function removeStarFlagKeyword(keyword) {
    const keywords = await loadStarFlagKeywords();
    const index = keywords.indexOf(keyword);
    if (index > -1) {
        keywords.splice(index, 1);
        await saveStarFlagKeywords(keywords);
        return true;
    }
    return false;
}
// 关键词管理
async function loadStarFlagKeywords() {
    return chrome.storage.local.get('starFlagKeywords').then(result => result.starFlagKeywords || []);
}
async function saveStarFlagKeywords(keywords) {
    return chrome.storage.local.set({ starFlagKeywords: keywords });
}

// 加载关键词列表
async function loadStarFlagKeywordList() {
    const keywordList = document.getElementById('starFlagKeywordList');
    if (!keywordList) return;

    const keywords = await loadStarFlagKeywords();
    
    keywordList.innerHTML = keywords.map(keyword => `
        <div class="keyword-item">
            <span>${keyword}</span>
            <button class="delete-keyword" data-keyword="${keyword}">×</button>
        </div>
    `).join('');

    // 添加删除事件监听
    keywordList.querySelectorAll('.delete-keyword').forEach(button => {
        button.onclick = async () => {
            const keyword = button.dataset.keyword;
            if (keyword) {
                const success = await removeStarFlagKeyword(keyword);
                if (success) {
                    loadStarFlagKeywordList(); // 重新加载关键词列表
                }
            }
        };
    });
}

//检查是否触发打星标动作
async function checkStarFlagEnabled() {
    //检查开关
    let result = await chrome.storage.local.get('starFlagKeywordEnabled');
    if(result.starFlagKeywordEnabled) {
        return true;
    }
    return false;
}
//检查是否触发加星标动作
async function checkStarFlagKeyword(msg) {
    
    //检查关键词
    const keywords = await loadStarFlagKeywords();
    if (msg && keywords.some(keyword => msg.includes(keyword))) {
        return true;
        
    }

    return false;
}
//添加星标
function actionStarFlag() {
    // 已星标用户直接忽略
    let active_star_flag = document.querySelector('.header-box .mark-chat.active');
    if(active_star_flag) {
        return true;  
    }
    let star_flag = document.querySelector('.header-box .mark-chat');
    if(star_flag) {
        star_flag.click();
        return true;
    }
    
    return false;
}

// 显示设置浮窗
function showSettingsWindow() {
    const settingsContent = `
        <div class="settings-container">
            <div class="section">
                <div class="section-title">转人工设置</div>
                <div class="option-group">
                    <div class="option-item">
                        <div class="main-switch-label">
                            自动转人工总开关
                            <div class="option-description">根据设定的规则自动转接人工客服，保存后生效</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="manualEnabled" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div class="option-item">
                        <div class="option-label">
                            关键词触发转人工
                            <div class="option-description">当客户输入内容包含以下关键词时自动转人工</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="keywordEnabled" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div class="option-item keyword-management" style="border-bottom: none;margin-top: -6px;">
                        <div class="keyword-input-group">
                            <input type="text" id="newKeyword" placeholder="输入触发转人工关键词">
                            <button id="addKeyword" class="keyword-btn">添加</button>
                        </div>
                        <div id="keywordList" class="keyword-list"></div>
                    </div>
                    
                    <div class="option-item">
                        <div class="option-label">
                            多媒体消息转人工
                            <div class="option-description">当客户发送图片/视频触发转人工</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="imageEnabled" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <div class="option-item">
                        <div class="option-label">
                            退款场景转人工
                            <div class="option-description">涉及退款相关问题时触发转人工</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="refundEnabled" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <div class="option-item">
                        <div class="option-label">
                            投诉场景转人工
                            <div class="option-description">客户投诉或表达不满时触发转人工</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="complaintEnabled" checked>
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div  class="option-item keyword-management" >
                        <div style="display:flex;">
                        <div class="option-label">
                            AI智能判断转人工
                            <div class="option-description">可在知识库添加触发条件，由AI进行判断自动转人工</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" id="difyTransferEnabled" checked>
                            <span class="slider"></span>
                        </label>
                       </div>
                       <div id="diykeywordList" class="keyword-list">
                            <div class="keyword-item">
                                <span>转人工</span>
                            </div>
                            <div class="keyword-item">
                                <span>转售前</span>
                            </div>
                            <div class="keyword-item">
                                <span>转售后</span>
                            </div>
                            <div class="keyword-item">
                                <span>转接</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="option-group">
                    <div class="option-item">
                        <div class="option-label">
                            自动转人工已关闭后处理方式
                             <div class="option-description">可设置发送提示语和自动标记星标</div>
                        </div>
                    </div>
                    <div class="option-item keyword-management">
                        <div class="transfer-fail-option">
                            <div class="transfer-fail-option-header">
                                <input type="checkbox" id="transferFailOption1" name="transferFailOptions" value="message" class="transfer-fail-checkbox" checked>
                                <label for="transferFailOption1">发送提示语</label>
                                <input type="checkbox" id="transferFailOption2" name="transferFailOptions" value="star" class="transfer-fail-checkbox">
                                <label for="transferFailOption2">自动标记星标</label>
                            </div>
                            <div class="keyword-input-group">
                                <input type="text" id="transferFailMessage" placeholder="请输入提示语" value="实在抱歉无法转接，请稍候再联系" class="transfer-fail-input">
                                <button id="transferFailMessageSave" class="transfer-fail-btn">修改</button>
                            </div>
                    </div>
                        <div id="transferFailMessageSaveSuccess" class="save-success">
                            <span class="settings-saved-tip">
                                 设置已保存
                            </span>
                        </div>
                    </div>
                    <div class="option-item" style="margin-top:-10px;">
                        <div class="option-label">
                            转人工客服失败后处理方式
                             <div class="option-description">转接异常或人工客服不在线时，发送提示语和打标星标</div>
                        </div>
                    </div>
                    <div class="option-item keyword-management">
                        <div class="transfer-fail-option">
                            <div class="transfer-fail-option-header">
                                <input type="checkbox" id="transferErrorOption1" name="transferErrorOptions" value="message" class="transfer-fail-checkbox" checked>
                                <label for="transferErrorOption1">发送提示语</label>
                                <input type="checkbox" id="transferErrorOption2" name="transferErrorOptions" value="star" class="transfer-fail-checkbox" checked>
                                <label for="transferErrorOption2">自动标记星标</label>
                            </div>
                            <div class="keyword-input-group">
                                <input type="text" id="transferErrorMessage" placeholder="请输入提示语" value="抱歉客服暂时离开，稍后为您处理" class="transfer-fail-input">
                                <button id="transferErrorMessageSave" class="transfer-fail-btn">修改</button>
                            </div>
                        </div>
                        <div id="transferErrorMessageSaveSuccess" class="save-success">
                            <span class="settings-saved-tip">
                                 设置已保存
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="section">
                <div class="section-title">人工客服设置</div>
                <div class="option-group">
                    <div class="option-item keyword-management">
                        <div class="main-switch-label">
                            转接到指定账号
                             <div class="option-description">请填客服账号或账号备注，多个账号随机选一个进行转接</div>
                        </div>
                        <div class="keyword-input-group">
                            <input type="text" id="targetServiceAccount" placeholder="输入当前店铺的一个客服账号">
                            <button id="addServiceAccount" class="keyword-btn">添加</button>
                        </div>
                        <div id="serviceAccountList" class="keyword-list"></div>
                        <div class="error-message" style="display: none;flex:100%;color:red;" id="targetServiceAccountError">请至少添加一个转接客服账号</div>
                    </div>
                    <!-- 暂时注释掉转接到分组功能
                    <div class="option-item" style="display: flex; align-items: center;">
                        <div class="main-switch-label">
                            转接到分组
                            <div class="option-description">选择一个客服分组，同时平台上创建对应名称客服分组</div>
                        </div>
                        <select id="defaultGroup" style="height: 36px;">
                            <option value="presales">售前组</option>
                            <option value="aftersales">售后组</option>
                        </select>
                    </div>
                    -->
                </div>
            </div>

            <button class="save-btn" id="saveSettings">保存设置</button>
            <div class="save-success" id="settingsSaveSuccess" style="display: none; text-align: center; margin-top: 12px;">
                <span class="settings-saved-tip">
                     设置已保存
                </span>
            </div>
        </div>
    `;

    const modal = createFloatingWindow('AI客服助手设置', settingsContent);

    // 加载设置
    loadSettings();
    
    // 加载关键词列表
    loadKeywordList();
    
    // 加载服务账号列表
    loadServiceAccountList();
    
    // 添加转人工相关设置保存功能
    const transferErrorMessageSave = document.getElementById('transferErrorMessageSave');
    const transferFailMessageSave = document.getElementById('transferFailMessageSave');
    const transferFailMessageSaveSuccess = document.getElementById('transferFailMessageSaveSuccess');
    const transferErrorMessageSaveSuccess = document.getElementById('transferErrorMessageSaveSuccess');
    
    // 转人工失败提示语保存按钮
    if (transferErrorMessageSave) {
        transferErrorMessageSave.addEventListener('click', async () => {
            const message = document.getElementById('transferErrorMessage').value;
            if (!message) return;
            
            const settings = await StateManager.getState('transferSettings', {});
            await StateManager.setState('transferSettings', {
                ...settings,
                transferErrorMessage: message
            });
            
            if (transferErrorMessageSaveSuccess) {
                // 显示保存成功提示，使用opacity实现淡入淡出效果
                transferErrorMessageSaveSuccess.style.display = 'block';
                transferErrorMessageSaveSuccess.style.opacity = '1';
                
                // 2秒后淡出
                setTimeout(() => {
                    transferErrorMessageSaveSuccess.style.opacity = '0';
                    setTimeout(() => {
                        transferErrorMessageSaveSuccess.style.display = 'none';
                    }, 300);
                }, 2000);
            }
        });
    }
    
    // 关闭自动转人工提示语保存按钮
    if (transferFailMessageSave) {
        transferFailMessageSave.addEventListener('click', async () => {
            const message = document.getElementById('transferFailMessage').value;
            if (!message) return;
            
            const settings = await StateManager.getState('transferSettings', {});
            await StateManager.setState('transferSettings', {
                ...settings,
                transferFailMessage: message
            });
            
            if (transferFailMessageSaveSuccess) {
                // 显示保存成功提示，使用opacity实现淡入淡出效果
                transferFailMessageSaveSuccess.style.display = 'block';
                transferFailMessageSaveSuccess.style.opacity = '1';
                
                // 2秒后淡出
                setTimeout(() => {
                    transferFailMessageSaveSuccess.style.opacity = '0';
                    setTimeout(() => {
                        transferFailMessageSaveSuccess.style.display = 'none';
                    }, 300);
                }, 2000);
            }
        });
    }
    
    // 监听复选框变化，自动保存选项
    const checkboxOptions = [
        'transferErrorOption1', 'transferErrorOption2',
        'transferFailOption1', 'transferFailOption2'
    ];
    
    checkboxOptions.forEach(id => {
        const checkbox = document.getElementById(id);
        if (checkbox) {
            checkbox.addEventListener('change', async () => {
                const settings = await StateManager.getState('transferSettings', {});
                
                // 根据复选框 ID 确定更新哪个选项
                if (id.startsWith('transferError')) {
                    // 更新转人工失败处理选项
                    const options = [
                        document.getElementById('transferErrorOption1')?.checked ? 'message' : '',
                        document.getElementById('transferErrorOption2')?.checked ? 'star' : ''
                    ].filter(Boolean);
                    
                    await StateManager.setState('transferSettings', {
                        ...settings,
                        transferErrorOptions: options
                    });
                } else if (id.startsWith('transferFail')) {
                    // 更新关闭自动转人工处理选项
                    const options = [
                        document.getElementById('transferFailOption1')?.checked ? 'message' : '',
                        document.getElementById('transferFailOption2')?.checked ? 'star' : ''
                    ].filter(Boolean);
                    
                    await StateManager.setState('transferSettings', {
                        ...settings,
                        transferFailOptions: options
                    });
                }
            });
        }
    });
    
    //增加添加关键词按钮的事件处理————modify by rice
    //2025-01-08
    const addKeywordButton = modal.querySelector('#addKeyword');
    addKeywordButton.onclick = addTransferKeyword;
    
    // 添加服务账号按钮事件
    const addServiceAccountButton = modal.querySelector('#addServiceAccount');
    if (addServiceAccountButton) {
        addServiceAccountButton.onclick = addServiceAccount;
    }

    // 添加保存按钮事件
    const saveButton = modal.querySelector('#saveSettings');
    saveButton.onclick = saveSettings;
    
    // 添加转人工失败提示文案保存按钮事件
    const transferFailMessageSaveButton = modal.querySelector('#transferFailMessageSave');
    if (transferFailMessageSaveButton) {
        transferFailMessageSaveButton.onclick = saveOtherSettings;
    }
    
    // 加载转人工失败提示文案
    chrome.storage.local.get('transferFailMessage').then(res => {
        if (res.transferFailMessage) {
            const transferFailMessageInput = document.querySelector('#transferFailMessage');
            if (transferFailMessageInput) {
                transferFailMessageInput.value = res.transferFailMessage;
            }
        } else {
            // 设置默认值
            const transferFailMessageInput = document.querySelector('#transferFailMessage');
            if (transferFailMessageInput) {
                transferFailMessageInput.value = '实在抱歉无法转接，请稍候再联系';
                // 保存默认值
                chrome.storage.local.set({ transferFailMessage: '实在抱歉无法转接，请稍候再联系' });
            }
        }
    }).catch(error => {
        // 出错时设置默认值
        const transferFailMessageInput = document.querySelector('#transferFailMessage');
        if (transferFailMessageInput) {
            transferFailMessageInput.value = '实在抱歉无法转接，请稍候再联系';
        }
    });
}

// 加载关键词列表
async function loadKeywordList() {
    try {
        // 加载关键词列表
        const keywords = await loadTransferKeywords();
        const keywordList = document.getElementById('keywordList');
        
        if (!keywordList) {
            debug('[关键词] 未找到关键词列表元素');
            return;
        }
        
        // 清空现有列表
        keywordList.innerHTML = '';
        
        // 添加每个关键词
        keywords.forEach(keyword => {
            const item = document.createElement('div');
            item.className = 'keyword-item';
            item.innerHTML = `
                <span>${keyword}</span>
                <button class="delete-keyword" data-keyword="${keyword}">×</button>
            `;
            keywordList.appendChild(item);
        });
        
        // 绑定删除按钮事件
        const deleteButtons = keywordList.querySelectorAll('.delete-keyword');
        deleteButtons.forEach(button => {
            button.addEventListener('click', () => {
                const keyword = button.getAttribute('data-keyword');
                removeTransferKeyword(keyword);
            });
        });
    } catch (error) {
        debug('[关键词] 加载关键词列表失败:', error);
    }
}

// 将样式添加到页面
function addStyles() {
    // 样式已通过content.css文件加载，但仍需添加一些动态样式
    debug('[样式] 附加样式已通过content.css加载');
    
    try {
        // 添加一些新的样式
        const additionalStyles = `
            .section-subtitle {
                font-weight: bold;
                margin-bottom: 8px;
                color: #333;
                font-size: 14px;
            }
            .option-checkbox-group {
                margin-bottom: 10px;
                display: flex;
                flex-direction: column;
            }
            .option-checkbox {
                display: flex;
                align-items: center;
                margin-bottom: 5px;
            }
            .option-checkbox input[type="checkbox"] {
                margin-right: 8px;
            }
        `;
        
        // 确保document.head存在再添加样式
        if (document.head) {
            const styleElement = document.createElement('style');
            styleElement.textContent = additionalStyles;
            document.head.appendChild(styleElement);
        } else {
            // 如果document.head不存在，等待DOM加载完成
            debug('[样式] DOM未完全加载，将在DOMContentLoaded事件后添加样式');
            document.addEventListener('DOMContentLoaded', () => {
                const styleElement = document.createElement('style');
                styleElement.textContent = additionalStyles;
                document.head.appendChild(styleElement);
            });
        }
    } catch (error) {
        debug('[样式] 添加样式时出错:', error);
    }
}

// 安全地执行添加样式，确保在DOM加载后运行
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addStyles);
} else {
addStyles();
}

// 显示关于浮窗
function showAboutWindow() {
    const aboutContent = `
        <div class="about-container">
            <div class="logo-section">
                <img src="${chrome.runtime.getURL('images/icon128.png')}" alt="AI客服助手" style="width: 80px; height: 80px;">
                <h3>AI客服助手</h3>
                <p class="version">版本 ${VERSION}</p>
            </div>
            
            <div class="description-section">
                <p>爱嘉客服是一款基于AI大模型构建的智能客服工具，旨在提高客服工作效率和服务质量。</p>
                <p>主要功能：</p>
                <ul>
                    <li>识别各种商品卡片等消息</li>
                    <li>AI自动回复，24小时在线</li>
                    <li>自动发送图片和视频，用户打标备注</li>
                    <li>人机结合，复杂问题自动转人工服务</li>
                    <li>基于AI能力，全面提升客服效率提升</li>
                </ul>
            </div>
            
            <div class="contact-section">
                <p>技术支持：</p>
                <p>邮箱：<EMAIL></p>
            </div>
        </div>
    `;
    
    createFloatingWindow('关于爱嘉智能客服', aboutContent);
}

// 加载转人工设置
async function loadSettings() {
    try {
        debug('[设置] 开始加载设置...');
        
        // 获取所有设置元素
        const elements = {
            manualEnabled: document.getElementById('manualEnabled'),
            keywordEnabled: document.getElementById('keywordEnabled'),
            imageEnabled: document.getElementById('imageEnabled'),
            refundEnabled: document.getElementById('refundEnabled'),
            complaintEnabled: document.getElementById('complaintEnabled'),
            difyTransferEnabled: document.getElementById('difyTransferEnabled'),
            targetServiceAccount: document.getElementById('targetServiceAccount')
        };

        // 检查所有元素是否存在
        const missingElements = Object.entries(elements)
            .filter(([key, element]) => !element)
            .map(([key]) => key);

        if (missingElements.length > 0) {
            throw new Error(`找不到以下设置元素: ${missingElements.join(', ')}`);
        }

        // 从存储中获取设置
        const transferSettings = await StateManager.getState('transferSettings', {
                manualEnabled: true,
                keywordEnabled: true,
                imageEnabled: true,
                refundEnabled: true,
            complaintEnabled: true,
            difyTransferEnabled: true,
            specifiedAgent: '',  // 默认空字符串
            serviceAccounts: [],  // 默认空数组
            transferFailMarkStar: false  // 转人工失败后是否自动打标
        });

        // 更新UI元素
        elements.manualEnabled.checked = transferSettings.manualEnabled;
        elements.keywordEnabled.checked = transferSettings.keywordEnabled;
        elements.imageEnabled.checked = transferSettings.imageEnabled;
        elements.refundEnabled.checked = transferSettings.refundEnabled;
        elements.complaintEnabled.checked = transferSettings.complaintEnabled;
        elements.difyTransferEnabled.checked = transferSettings.difyTransferEnabled;
        elements.targetServiceAccount.value = transferSettings.specifiedAgent || '';
        
        // 设置关闭自动转人工处理方式选项
        const transferFailOption1 = document.getElementById('transferFailOption1');
        const transferFailOption2 = document.getElementById('transferFailOption2');
        if (transferFailOption1 && transferFailOption2) {
            // 检查是否需要发送消息和标记星标
            transferFailOption1.checked = transferSettings.transferFailOptions ? 
                transferSettings.transferFailOptions.includes('message') : true;
            transferFailOption2.checked = transferSettings.transferFailOptions ? 
                transferSettings.transferFailOptions.includes('star') : false;
                
            // 设置提示语
        const transferFailMessage = document.getElementById('transferFailMessage');
        if (transferFailMessage) {
            transferFailMessage.value = transferSettings.transferFailMessage || '实在抱歉无法转接，请稍候再联系';
            }
        }
        
        // 设置转人工失败处理方式选项
        const transferErrorOption1 = document.getElementById('transferErrorOption1');
        const transferErrorOption2 = document.getElementById('transferErrorOption2');
        if (transferErrorOption1 && transferErrorOption2) {
            // 检查是否需要发送消息和标记星标
            transferErrorOption1.checked = transferSettings.transferErrorOptions ? 
                transferSettings.transferErrorOptions.includes('message') : true;
            transferErrorOption2.checked = transferSettings.transferErrorOptions ? 
                transferSettings.transferErrorOptions.includes('star') : false;
            
            // 设置失败提示语
            const transferErrorMessage = document.getElementById('transferErrorMessage');
            if (transferErrorMessage) {
                transferErrorMessage.value = transferSettings.transferErrorMessage || '抱歉客服暂时离开，稍后为您处理';
            }
        }

        // 根据总开关状态设置子开关和相关功能区域的禁用状态
        const subSwitches = ['keywordEnabled', 'imageEnabled', 'refundEnabled', 'complaintEnabled', 'difyTransferEnabled'];
        
        // 控制子开关状态
        subSwitches.forEach(switchId => {
            const element = document.getElementById(switchId);
            if (element) {
                element.disabled = !transferSettings.manualEnabled;
                element.parentElement.style.opacity = transferSettings.manualEnabled ? '1' : '0.5';
            }
        });
        
        // 控制所有子功能区域状态，但排除转人工相关处理方式部分
        const transferSubSections = document.querySelectorAll('.option-item:not(:first-child)');
        transferSubSections.forEach(section => {
            // 检查是否为转人工失败处理方式部分或转人工客服失败后处理方式部分
            const isTransferFailSection = section.querySelector('.transfer-fail-option') !== null;
            const isErrorTitle = section.textContent && section.textContent.includes('转人工客服失败后处理方式');
            
            // 如果是转人工相关处理方式部分，则不应用灰化和禁用
            if (!isTransferFailSection && !isErrorTitle) {
                // 设置透明度
                section.style.opacity = transferSettings.manualEnabled ? '1' : '0.5';
                
                // 禁用所有输入框、按钮和交互元素
                const interactiveElements = section.querySelectorAll('input, button, select, textarea, .keyword-item');
                interactiveElements.forEach(el => {
                    el.disabled = !transferSettings.manualEnabled;
                    
                    // 对于关键词删除按钮特殊处理
                    if (el.classList.contains('delete-keyword') || el.classList.contains('delete-account')) {
                        el.style.pointerEvents = transferSettings.manualEnabled ? 'auto' : 'none';
                    }
                });
            }
        });

        // 添加总开关的事件监听器
        if (elements.manualEnabled) {
            // 移除可能存在的旧事件监听器，避免重复
            elements.manualEnabled.removeEventListener('change', handleManualEnabledChange);
            // 添加新的事件监听器
            elements.manualEnabled.addEventListener('change', handleManualEnabledChange);
        }

        debug('[设置] 加载成功，指定客服账号:', transferSettings.specifiedAgent);
        debug('[设置] 加载成功，客服账号列表:', transferSettings.serviceAccounts || []);
    } catch (error) {
        debug('[设置] 加载失败:', error);
    }
}

// 处理总开关状态变化的函数
function handleManualEnabledChange(event) {
    const isEnabled = event.target.checked;
    debug('[设置] 自动转人工总开关状态变更为:', isEnabled ? '开启' : '关闭');
    
    // 更新子开关状态
    const subSwitches = ['keywordEnabled', 'imageEnabled', 'refundEnabled', 'complaintEnabled', 'difyTransferEnabled'];
    subSwitches.forEach(switchId => {
        const element = document.getElementById(switchId);
        if (element) {
            element.disabled = !isEnabled;
            element.parentElement.style.opacity = isEnabled ? '1' : '0.5';
        }
    });
    
    // 更新所有子功能区域状态，但排除转人工相关处理方式部分
    const transferSubSections = document.querySelectorAll('.option-item:not(:first-child)');
    transferSubSections.forEach(section => {
        // 检查是否为转人工失败处理方式部分或转人工客服失败后处理方式部分
        const isTransferFailSection = section.querySelector('.transfer-fail-option') !== null;
        const isErrorTitle = section.textContent && section.textContent.includes('转人工客服失败后处理方式');
        
        // 如果是转人工相关处理方式部分，则不应用灰化和禁用
        if (!isTransferFailSection && !isErrorTitle) {
            // 设置透明度
            section.style.opacity = isEnabled ? '1' : '0.5';
            
            // 禁用所有输入框、按钮和交互元素
            const interactiveElements = section.querySelectorAll('input, button, select, textarea, .keyword-item');
            interactiveElements.forEach(el => {
                el.disabled = !isEnabled;
                
                // 对于关键词删除按钮特殊处理
                if (el.classList.contains('delete-keyword') || el.classList.contains('delete-account')) {
                    el.style.pointerEvents = isEnabled ? 'auto' : 'none';
                }
            });
        }
    });
}

// 保存转人工设置
async function saveSettings() {
    const saveButton = document.getElementById('saveSettings');
    const saveSuccess = document.getElementById('settingsSaveSuccess');
    
    try {
        saveButton.disabled = true;
        saveButton.textContent = '保存中...';
        
        // 获取指定客服账号列表
        const serviceAccounts = await loadServiceAccounts();
        
        if(document.getElementById('manualEnabled')?.checked && serviceAccounts.length === 0){
            //错误提示
            document.getElementById('targetServiceAccountError').style.display = 'block';
            throw new Error('请至少添加一个转接客服账号');
        }else {
            document.getElementById('targetServiceAccountError').style.display = 'none';
        }

        // 获取当前存储的设置，以保留转人工失败处理方式的值
        const existingSettings = await StateManager.getState('transferSettings', {});

        // 保存设置
        await StateManager.setState('transferSettings', {
            manualEnabled: document.getElementById('manualEnabled')?.checked ?? true,
            keywordEnabled: document.getElementById('keywordEnabled')?.checked ?? true,
            imageEnabled: document.getElementById('imageEnabled')?.checked ?? true,
            refundEnabled: document.getElementById('refundEnabled')?.checked ?? true,
            complaintEnabled: document.getElementById('complaintEnabled')?.checked ?? true,
            difyTransferEnabled: document.getElementById('difyTransferEnabled')?.checked ?? true,
            specifiedAgent: serviceAccounts.length > 0 ? serviceAccounts[0] : '',  // 兼容旧版本，保留第一个账号作为默认
            serviceAccounts: serviceAccounts,  // 保存所有客服账号
            
            // 保存转人工失败处理方式相关设置
            transferErrorOptions: [
                document.getElementById('transferErrorOption1')?.checked ? 'message' : '',
                document.getElementById('transferErrorOption2')?.checked ? 'star' : ''
            ].filter(Boolean),
            transferErrorMessage: document.getElementById('transferErrorMessage')?.value || '抱歉客服暂时离开，稍后为您处理',
            
            // 保存关闭自动转人工处理方式相关设置
            transferFailOptions: [
                document.getElementById('transferFailOption1')?.checked ? 'message' : '',
                document.getElementById('transferFailOption2')?.checked ? 'star' : ''
            ].filter(Boolean),
            transferFailMessage: document.getElementById('transferFailMessage')?.value || '实在抱歉无法转接，请稍候再联系'
        });

        if (saveSuccess) {
            const savedTip = saveSuccess.querySelector('.settings-saved-tip');
            
            saveSuccess.style.display = 'block';
            saveSuccess.style.opacity = '1';
            
            setTimeout(() => {
                saveSuccess.style.opacity = '0';
                setTimeout(() => {
                    saveSuccess.style.display = 'none';
                }, 300);
            }, 2000);
        }

        debug('[设置] 保存成功，指定客服账号列表:', serviceAccounts);

    } catch (error) {
        debug('[设置] 保存失败:', error);
        if (saveSuccess) {
            saveSuccess.textContent = '保存失败，请重新打开再保存';
            saveSuccess.style.color = '#dc2626';
            saveSuccess.style.display = 'block';
            
            setTimeout(() => {
                saveSuccess.style.opacity = '0';
                setTimeout(() => {
                    saveSuccess.style.display = 'none';
                }, 300);
            }, 2000);
        }
    } finally {
        saveButton.disabled = false;
        saveButton.textContent = '保存设置';
    }
}

// 关键词管理
async function loadTransferKeywords() {
    return new Promise(resolve => {
        chrome.storage.local.get(['transferKeywords'], result => {
            const keywords = result.transferKeywords || [];
            debug('[关键词] 加载关键词列表:', keywords);
            resolve(keywords);
        });
    });
}

async function actionModifyAddress(card){
    if (card.textContent.includes('申请修改为新地址') && card.textContent.includes('同意修改') && card.textContent.includes('商品已发货，不可修改')) {
        //当前卡片为转人工卡片
        debug('[改地址] 检测到修改地址卡片');
        
        // 提取卡片信息
        const cardInfo = extractCardInfo(card);
        if (!cardInfo || !cardInfo.goodsName || !cardInfo.price) {
            debug('[改地址] 无法提取卡片信息，默认转人工');
            return 'transfer';
        }
        
        // 获取订单状态
        const orderStatus = await findMatchingOrderStatus(cardInfo);
        debug('[改地址] 订单状态码:', orderStatus);
        
        let card_op_btns = card.querySelectorAll('button');
        
        if(orderStatus === 1){
            //待发货
            debug('[改地址] 订单待发货，自动同意修改地址');
            //模拟点击同意
            if(card_op_btns[1]) {
                // 确保按钮在视图中
                card_op_btns[1].scrollIntoView({ behavior: 'smooth', block: 'center' });
                await sleep(500);
                
                // 模拟真实点击
                let rect = card_op_btns[1].getBoundingClientRect();
                let clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window,
                    clientX: rect.left + rect.width / 2,
                    clientY: rect.top + rect.height / 2
                });
                card_op_btns[1].dispatchEvent(clickEvent);

            }
            return true;
        } else {
            // 非待发货状态，默认自动不同意
            debug('[改地址] 订单非待发货状态，执行自动不同意操作');
            if(card_op_btns[0]) {
                // 确保按钮在视图中
                card_op_btns[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                await sleep(500);
                
                // 模拟真实点击
                let rect = card_op_btns[0].getBoundingClientRect();
                let clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window,
                    clientX: rect.left + rect.width / 2,
                    clientY: rect.top + rect.height / 2
                });
                card_op_btns[0].dispatchEvent(clickEvent);
                await sleep(500);
            }
            return 'rejected'; // 返回状态，表示已自动拒绝
        }
    }
    return false;
}


//检查修改地址卡片消息,返回订单状态，用于后续处理
async function modifyAddressCheck(card){
    // 先检查是否是售后申请卡片，如果是则直接返回false，不走修改地址逻辑
    if (card.querySelector('.apply-card') && 
        (card.textContent.includes('消费者申请售后') || 
         card.textContent.includes('退款') || 
         card.textContent.includes('退货'))) {
        debug('[改地址] 检测到售后申请卡片，不走修改地址逻辑');
        return false;
    }
    
    //检查是否开启自动同意修改地址
    return await chrome.storage.local.get(['modifyAddressEnabled', 'modifyAddressDisabledMessage']).then(async res=> {
        debug('[改地址] 自动同意修改地址开关状态:', res.modifyAddressEnabled);
        
        // 检查转人工总开关状态
        const transferSettings = await StateManager.getState('transferSettings', {
            manualEnabled: true  // 总开关默认开启
        });
        
        debug('[改地址] 转人工总开关状态:', transferSettings.manualEnabled);
        
        if(res.modifyAddressEnabled){
            // 自动同意修改地址功能开启
            return actionModifyAddress(card);
        } else {
            // 自动同意修改地址功能关闭，直接返回自定义提示语
            debug('[改地址] 自动同意修改地址功能已关闭，返回自定义提示语');
            // 获取自定义提示语或使用默认值
            const message = res.modifyAddressDisabledMessage || '抱歉不支持改地址哦，建议重新拍下';
            return {type: 'disabled', message: message};
        }
    }).catch(error => {
        debug('[改地址] 自动同意修改地址开关状态读取异常，跳过检查...', error);
        return false;
    });
}

//返回当前用户的订单状态，-1异常 0其他 1为待发货，2为已发货，3为已完成，4为已取消
async function getCurrentOrderStatusFromUser(){
    //获取订单状态
    let bar_items = document.querySelectorAll('.bar-box.four-tab .bar-item');

    for (bar_item_tab of bar_items) {
        if(bar_item_tab.textContent.trim() === '最新订单') {
            //模拟点击最新订单
            bar_item_tab.click();

            if (await waitForElementOrTimeout('.order-panel-second-bar', 5000)) {
                // 执行你的回调函数
                let order_panel_second_bar = document.querySelectorAll('.order-panel-second-bar');
                if(order_panel_second_bar[0] && order_panel_second_bar[0].textContent.trim() === '个人订单') {
                    order_panel_second_bar[0].click();

                    if (await waitForElementOrTimeout('.order-item-list .order-item', 5000)) {
                        //个人订单面板
                        let order_items = document.querySelectorAll('.order-item-list .order-item');
                        if(order_items.length > 0) {
                            //获取订单状态
                            let first_order_item = order_items[0];
                            return first_order_item;
                        }
                        
                        return null;
                    }
                }
            }
            
        }
    }

    return -1;
}

// 使用 MutationObserver 等待元素出现，或超时的函数
function waitForElementOrTimeout(selector, timeout) {
    return Promise.race([
        waitForElement(selector),
        new Promise(resolve => setTimeout(() => resolve(false), timeout))
    ]);
}

// 使用 MutationObserver 等待元素出现的函数 (同方案二)
function waitForElement(selector) {
    return new Promise(resolve => {
        const observer = new MutationObserver((mutationsList, observer) => {
            const element = document.querySelector(selector);
            if (element) {
                observer.disconnect();
                resolve(true);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    });
}


async function saveTransferKeywords(keywords) {
    return new Promise(resolve => {
        chrome.storage.local.set({ transferKeywords: keywords }, () => {
            debug('[关键词] 保存关键词列表:', keywords);
            resolve(true);
        });
    });
}

async function addTransferKeyword() {
    let keyword = document.getElementById('newKeyword').value;
    if (!keyword || typeof keyword !== 'string') {
        debug('[关键词] 无效的关键词');
        return false;
    }

    keyword = keyword.trim();
    if (keyword.length === 0) {
        debug('[关键词] 关键词不能为空');
        return false;
    }

    const keywords = await loadTransferKeywords();
    if (keywords.includes(keyword)) {
        debug('[关键词] 关键词已存在:', keyword);
        return false;
    }

    keywords.push(keyword);
    await saveTransferKeywords(keywords);
    debug('[关键词] 添加关键词成功:', keyword);
    //重置输入框
    document.getElementById('newKeyword').value = "";
    await loadKeywordList(); // 重新加载关键词列表
    return true;
}

async function removeTransferKeyword(keyword) {
    const keywords = await loadTransferKeywords();
    const index = keywords.indexOf(keyword);
    if (index > -1) {
        keywords.splice(index, 1);
        await saveTransferKeywords(keywords);
        debug('[关键词] 删除关键词成功:', keyword);
        return true;
    }
    return false;
}

// 默认转人工关键词
const DEFAULT_TRANSFER_KEYWORDS = ['转人工', '人工客服', '人工服务', '真人客服', '转接人工'];

// 初始化关键词列表
async function initializeKeywords() {
    const existingKeywords = await loadTransferKeywords();
    if (existingKeywords.length === 0) {
        // 如果关键词列表为空，添加默认关键词
        await saveTransferKeywords(DEFAULT_TRANSFER_KEYWORDS);
        debug('[关键词] 已初始化默认关键词:', DEFAULT_TRANSFER_KEYWORDS);
    }
}

// 初始化消息处理器
async function initMessageHandler() {
    try {
        debug('[MessageBoxHandler] 正在初始化消息处理器...');
        
        // 等待 MessageBoxHandler 类加载完成
        if (!window.MessageBoxHandler) {
            debug('[MessageBoxHandler] 等待类定义加载...');
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // 创建实例并挂载到全局
        if (!window.messageBoxHandlerInstance) {
            debug('[MessageBoxHandler] 创建新实例...');
            window.messageBoxHandlerInstance = new MessageBoxHandler();
        }

        const handler = window.messageBoxHandlerInstance;
        if (!handler) {
            debug('[MessageBoxHandler] 错误: 创建实例失败');
            return null;
        }

        // 启动观察
        if (typeof handler.startObserving === 'function') {
            handler.startObserving();
        debug('[MessageBoxHandler] 消息处理器初始化成功');
        } else {
            debug('[MessageBoxHandler] 错误: startObserving 方法未定义');
        }

        return handler;
    } catch (error) {
        debug('[MessageBoxHandler] 初始化失败:', error);
        return null;
    }
}

// 添加一个全局变量用于跟踪转移会话的MutationObserver
let transferDialogObserver = null;

// 监听并自动处理转移会话弹窗
function startTransferDialogObserver() {
    debug('[转移会话] 启动转移会话弹窗检查...');
    
    // 如果已经存在观察器，先断开连接
    if (transferDialogObserver) {
        transferDialogObserver.disconnect();
        transferDialogObserver = null;
    }
    
    // 不再创建新的观察器，只检查一次当前页面上的转移会话弹窗
    // 移除MutationObserver的持续监听，只保留手动检查功能
    
    // 立即检查页面上是否已经存在转移会话弹窗
    checkExistingTransferDialogs();
    
    debug('[转移会话] 转移会话弹窗检查已完成');
}

// 检查当前页面上是否已经存在转移会话弹窗
function checkExistingTransferDialogs() {
    try {
        // 查找所有可能的转移会话弹窗
        const dialogs = document.querySelectorAll('.el-dialog__wrapper, .el-dialog');
        if (dialogs.length === 0) return;
        
        debug(`[转移会话] 检查页面上已存在的${dialogs.length}个对话框`);
        
        // 增加更广泛的选择器，确保能找到所有可能的转移会话弹窗
        const allPossibleDialogs = [
            ...dialogs,
            ...document.querySelectorAll('.el-message-box__wrapper'),
            ...document.querySelectorAll('[role="dialog"]'),
            ...document.querySelectorAll('[aria-label="转移会话"]')
        ];
        
        // 去重
        const uniqueDialogs = [...new Set(allPossibleDialogs)];
        debug(`[转移会话] 使用扩展选择器找到${uniqueDialogs.length}个可能的对话框`);
        
        uniqueDialogs.forEach(dialog => {
            checkForTransferDialog(dialog);
        });
        
        // 特别检查包含"消息发送失败"的元素
        const allElements = document.querySelectorAll('*');
        for (const element of allElements) {
            if (element.textContent && element.textContent.includes('消息发送失败') && 
                element.textContent.includes('转移会话')) {
                debug('[转移会话] 找到包含"消息发送失败"和"转移会话"的元素');
                checkForTransferDialog(element.closest('[role="dialog"]') || element.parentElement);
            }
        }
    } catch (error) {
        debug('[转移会话] 检查现有对话框时出错:', error);
    }
}

// 检查元素是否是转移会话弹窗
function checkForTransferDialog(element) {
    try {
        // 检查所有可能的弹窗结构
        let dialog = null;
        
        // 情况1: 元素本身就是el-dialog
        if (element.classList && element.classList.contains('el-dialog')) {
            dialog = element;
        }
        // 情况2: 元素是el-dialog__wrapper
        else if (element.classList && element.classList.contains('el-dialog__wrapper')) {
            dialog = element.querySelector('.el-dialog');
        }
        // 情况3: 元素内部包含el-dialog
        else if (element.querySelector && element.querySelector('.el-dialog')) {
            dialog = element.querySelector('.el-dialog');
        }
        // 情况4: 元素内部包含el-dialog__wrapper
        else if (element.querySelector && element.querySelector('.el-dialog__wrapper')) {
            dialog = element.querySelector('.el-dialog__wrapper .el-dialog');
        }
        
        // 如果没找到对话框，直接返回
        if (!dialog) return;
        
        // 检查是否是转移会话对话框
        const title = dialog.querySelector('.el-dialog__title');
        if (!title || title.textContent.trim() !== '转移会话') {
            return;
        }
        
        debug('[转移会话] 检测到转移会话弹窗');
        
        // 检查是否包含消息发送失败的内容
        // 使用更宽松的检查，查找任何包含"消息发送失败"的元素
        const dialogBody = dialog.querySelector('.el-dialog__body');
        if (!dialogBody) return;
        
        const bodyText = dialogBody.textContent || '';
        if (!bodyText.includes('消息发送失败')) {
            return;
        }
        
        debug('[转移会话] 检测到消息发送失败的转移会话弹窗');
        
        // 查找转移会话按钮
        // 方法1: 通过类名查找
        let transferButton = dialog.querySelector('.el-button--primary');
        
        // 方法2: 如果方法1失败，通过文本内容查找
        if (!transferButton) {
            const buttons = dialog.querySelectorAll('button');
            for (const btn of buttons) {
                if (btn.textContent && btn.textContent.includes('转移会话')) {
                    transferButton = btn;
                    break;
                }
            }
        }
        
        // 方法3: 如果前两种方法都失败，查找包含span的按钮
        if (!transferButton) {
            const spans = dialog.querySelectorAll('button span');
            for (const span of spans) {
                if (span.textContent && span.textContent.includes('转移会话')) {
                    transferButton = span.closest('button');
                    break;
                }
            }
        }
        
        if (transferButton) {
            debug('[转移会话] 找到转移会话按钮，准备点击');
            
            // 延迟点击，避免太快
            setTimeout(() => {
                transferButton.click();
                debug('[转移会话] 已自动点击转移会话按钮');
                
                // 点击后立即刷新页面
                debug('[转移会话] 立即自动刷新页面...');
                window.location.reload();
            }, 500);
        } else {
            debug('[转移会话] 未找到转移会话按钮，弹窗结构:', dialog.outerHTML.substring(0, 200));
        }
    } catch (error) {
        debug('[转移会话] 处理转移会话弹窗时出错:', error);
    }
}

// 在页面加载完成后初始化
window.addEventListener('load', async () => {
    debug('Content script initializing...');
    
    //初始化悬浮球
    if (!floatingBallInstance) {
        floatingBallInstance = new FloatingBall();
        floatingBallInstance.create();
        window.floatingBall = floatingBallInstance;
    }

    // 移除自动启动转移会话弹窗监听，仅在检测到未读消息时触发检查
    // startTransferDialogObserver();

    setTimeout(async () => {
        debug('页面已加载完成，开始初始化...');
        await initMessageHandler();
        init();
        startPdd();
    }, 2000);
});


function startPdd() {
    //检查全局自动回复开关是否开启
    if(!window.floatingBall.autoReplyEnabled){
        debug('[startPdd] 全局自动回复开关未开启，跳过初始化...');
        return;
    }

    // 检查并设置客服在线状态
    checkAndSetOnlineStatus();
    
    // 检查并切换到今日接待
    checkAndSwitchToTodayConversation();
    
    // 延迟5秒后再次检查在线状态，确保状态已正确设置
    setTimeout(() => {
        checkAndSetOnlineStatus();
    }, 5000);
    
    // 确保健康检查定时器已创建
    if (!window.healthCheckTimer && !window.healthCheckTimerCreating) {
        debug('[startPdd] 重新创建健康检查定时器');
        window.createHealthCheckTimer();
    }
    
    // 启动消息监听
    listenToPDDMessages();
}

// 检查并设置客服在线状态为"在线"
function checkAndSetOnlineStatus() {
    try {
        debug('[在线状态] 开始检查客服在线状态...');
        
        // 查找状态元素 - 使用更精确的选择器
        const statusElement = document.querySelector('.header.panel-tab-header .status');
        if (!statusElement) {
            debug('[在线状态] 未找到状态元素，尝试备用选择器');
            // 备用选择器
            const altStatusElement = document.querySelector('.status');
            if (!altStatusElement) {
                debug('[在线状态] 未找到状态元素');
                return;
            }
            debug('[在线状态] 使用备用选择器找到状态元素');
            checkAndUpdateStatus(altStatusElement);
            return;
        }
        
        checkAndUpdateStatus(statusElement);
    } catch (error) {
        debug('[在线状态] 设置在线状态时出错:', error);
    }
}

// 检查并更新状态
function checkAndUpdateStatus(statusElement) {
    // 检查当前状态
    const currentStatus = statusElement.className;
    debug('[在线状态] 当前状态:', currentStatus);
    
    // 如果已经是在线状态，无需操作
    if (currentStatus.includes('online')) {
        debug('[在线状态] 已经是在线状态，无需修改');
        return;
    }
    
    // 点击状态图标打开下拉菜单
    debug('[在线状态] 当前不是在线状态，准备修改...');
    statusElement.click();
    
    // 等待下拉菜单出现
    setTimeout(() => {
        // 尝试多种选择器查找"在线"选项
        const selectors = [
            '.status-box li p span',
            '.status-box ul li:first-child p',
            '.status-box ul li p span:contains("在线")'
        ];
        
        let onlineOption = null;
        
        // 尝试第一个选择器 - 查找第一个选项的span
        onlineOption = document.querySelector(selectors[0]);
        if (onlineOption && onlineOption.textContent.trim() === '在线') {
            debug('[在线状态] 通过选择器1找到"在线"选项，准备点击');
            onlineOption.parentElement.click();
            debug('[在线状态] 已点击"在线"选项');
            return;
        }
        
        // 尝试第二个选择器 - 直接查找第一个li的p元素
        onlineOption = document.querySelector(selectors[1]);
        if (onlineOption) {
            debug('[在线状态] 通过选择器2找到第一个选项，准备点击');
            onlineOption.click();
            debug('[在线状态] 已点击第一个选项');
            return;
        }
        
        // 如果上述方法都失败，尝试遍历所有选项
        const allOptions = document.querySelectorAll('.status-box li p');
        for (const option of allOptions) {
            if (option.textContent.includes('在线')) {
                debug('[在线状态] 通过遍历找到包含"在线"的选项，准备点击');
                option.click();
                debug('[在线状态] 已点击包含"在线"的选项');
                return;
            }
        }
        
        // 如果仍然找不到，尝试直接点击第一个li元素
        const firstLi = document.querySelector('.status-box li');
        if (firstLi) {
            debug('[在线状态] 未找到明确的"在线"选项，尝试点击第一个选项');
            firstLi.click();
            debug('[在线状态] 已点击第一个选项');
        } else {
            debug('[在线状态] 未找到任何可点击的选项');
        }
    }, 500);
}

// 检查并切换到今日接待
function checkAndSwitchToTodayConversation() {
    try {
        debug('[会话列表] 开始检查当前会话列表...');
        
        // 查找今日接待元素 - 使用更精确的选择器
        let todayConvElement = document.querySelector('.conv-today');
        
        // 如果未找到，尝试其他选择器
        if (!todayConvElement) {
            debug('[会话列表] 未找到今日接待元素，尝试备用选择器');
            todayConvElement = document.querySelector('div[class*="conv-today"]');
            
            if (!todayConvElement) {
                debug('[会话列表] 使用备用选择器仍未找到今日接待元素');
                
                // 尝试查找包含"今日接待"文本的元素
                const allElements = document.querySelectorAll('div');
                for (const element of allElements) {
                    if (element.textContent && element.textContent.includes('今日接待')) {
                        todayConvElement = element;
                        debug('[会话列表] 通过文本内容找到今日接待元素');
                        break;
                    }
                }
                
                if (!todayConvElement) {
                    debug('[会话列表] 未找到今日接待元素');
                    return;
                }
            }
        }
        
        // 检查今日接待是否已选中
        if (todayConvElement.classList.contains('sel')) {
            debug('[会话列表] 已经是今日接待，无需切换');
            return;
        }
        
        // 点击今日接待
        debug('[会话列表] 当前不是今日接待，准备切换...');
        todayConvElement.click();
        debug('[会话列表] 已点击今日接待');
        
        // 延迟检查是否切换成功
        setTimeout(() => {
            if (!todayConvElement.classList.contains('sel')) {
                debug('[会话列表] 切换可能不成功，再次尝试点击');
                todayConvElement.click();
            } else {
                debug('[会话列表] 确认已成功切换到今日接待');
            }
        }, 1000);
    } catch (error) {
        debug('[会话列表] 切换到今日接待时出错:', error);
    }
}

// 初始化设置
chrome.storage.local.get([
    'manualEnabled',
    'keywordEnabled',
    'imageEnabled',
    'refundEnabled',
    'complaintEnabled'
], (settings) => {
    // 确保默认值设置正确
    chrome.storage.local.set({
        manualEnabled: settings.manualEnabled !== false, // 默认为true
        keywordEnabled: settings.keywordEnabled !== false, // 默认为true
        imageEnabled: settings.imageEnabled !== false, // 默认为true
        refundEnabled: settings.refundEnabled !== false, // 默认为true
        complaintEnabled: settings.complaintEnabled !== false, // 默认为true
        difyTransferEnabled: settings.difyTransferEnabled !== false // 默认为true
    }, () => {
        debug('[设置] 初始化设置完成');
    });
});

// 统一的状态管理
const StateManager = {
    // 获取状态
    async getState(key, defaultValue) {
        return new Promise(resolve => {
            chrome.storage.local.get({ [key]: defaultValue }, result => {
                resolve(result[key]);
            });
        });
    },

    // 设置状态
    async setState(key, value) {
        return new Promise(resolve => {
            chrome.storage.local.set({ [key]: value }, () => {
                resolve(true);
            });
        });
    },

    // 监听状态变化
    addListener(callback) {
        chrome.storage.onChanged.addListener((changes, namespace) => {
            if (namespace === 'local') {
                callback(changes);
            }
        });
    }
};

// 创建设置对话框
function createSettingsDialog() {
  const settingsContent = document.createElement('div');
  settingsContent.className = 'settings-content';
  settingsContent.innerHTML = `
    <div class="settings-body">
      <div class="section">
        <h3 class="section-title">自动转人工场景</h3>
        <div class="option-group">
          <div class="option-item">
            <div class="option-label">
              <div>启用自动转人工</div>
              <div class="option-description">开启后，将根据以下场景自动转人工</div>
            </div>
            <label class="switch">
              <input type="checkbox" id="autoTransferEnabled" checked>
              <span class="slider"></span>
            </label>
          </div>
          <div class="option-item">
            <div class="option-label">
              <div>关键词触发</div>
              <div class="option-description">当用户输入包含关键词时自动转人工</div>
            </div>
            <label class="switch">
              <input type="checkbox" id="keywordTriggerEnabled" checked>
              <span class="slider"></span>
            </label>
          </div>
          <div class="option-item">
            <div class="option-label">
              <div>多媒体消息转人工</div>
              <div class="option-description">当客户发送图片/视频自动转人工</div>
            </div>
            <label class="switch">
              <input type="checkbox" id="imageTransferEnabled">
              <span class="slider"></span>
            </label>
          </div>
          <div class="option-item">
            <div class="option-label">
              <div>退款场景转人工</div>
              <div class="option-description">当用户提及退款相关内容时自动转人工</div>
            </div>
            <label class="switch">
              <input type="checkbox" id="refundTransferEnabled" checked>
              <span class="slider"></span>
            </label>
          </div>
          <div class="option-item">
            <div class="option-label">
              <div>投诉场景转人工</div>
              <div class="option-description">当用户提及投诉相关内容时自动转人工</div>
            </div>
            <label class="switch">
              <input type="checkbox" id="complaintTransferEnabled" checked>
              <span class="slider"></span>
            </label>
          </div>
        </div>
      </div>

      <div class="section">
        <h3 class="section-title">转人工关键词</h3>
        <div class="keyword-group">
          <div class="keyword-input">
            <input type="text" id="keywordInput" placeholder="请输入关键词">
            <button class="add-keyword">添加</button>
          </div>
          <div class="keyword-list">
            <div class="keyword-item">
              人工客服<button class="remove-keyword">×</button>
            </div>
            <div class="keyword-item">
              转人工<button class="remove-keyword">×</button>
            </div>
            <div class="keyword-item">
              退款<button class="remove-keyword">×</button>
            </div>
          </div>
        </div>
      </div>

      <div class="section">
        <h3 class="section-title">客服组设置</h3>
        <div class="option-group">
          <div class="option-item">
            <div class="option-label">
              <div>默认转接组</div>
              <div class="option-description">选择默认转接的客服组</div>
            </div>
            <select id="defaultGroup">
              <option value="1">售前客服组</option>
              <option value="2">售后客服组</option>
              <option value="3">VIP客服组</option>
            </select>
          </div>
          <div class="option-item">
            <div class="option-label">
              <div>指定转接账号</div>
              <div class="option-description">可选填，优先转接到指定客服</div>
            </div>
            <input type="text" id="specificAgent" placeholder="请输入客服工号">
          </div>
        </div>
      </div>
    </div>
    <div class="settings-footer">
      <button class="cancel-btn">取消</button>
      <button class="save-btn">保存</button>
    </div>
  `;

  // 添加保存成功提示元素
  const saveSuccess = document.createElement('div');
  saveSuccess.className = 'save-success';
  saveSuccess.textContent = '保存成功';
  document.body.appendChild(saveSuccess);

  // 绑定保存按钮事件
  const saveBtn = settingsContent.querySelector('.save-btn');
  saveBtn.addEventListener('click', () => {
    // 显示保存成功提示
    saveSuccess.classList.add('visible');
    setTimeout(() => {
      saveSuccess.classList.remove('visible');
    }, 1500);
  });

  // 绑定取消按钮事件
  const cancelBtn = settingsContent.querySelector('.cancel-btn');
  cancelBtn.addEventListener('click', () => {
    settingsContent.remove();
    saveSuccess.remove();
  });

  return settingsContent;
}

// 显示 AI 设置
async function showAISettings() {
    const { createAISettingsDialog, initializeAISettings } = await import('../config/config.js');
    const dialog = createAISettingsDialog();
    document.body.appendChild(dialog);
    await initializeAISettings(dialog);
}



// AI 设置相关样式已移至 content.css 文件

//dify返回的回答，进行二次识别处理
//输入dify的回复
/**
 * 发送订单信息到DIFY进行处理
 * @param {Object} orderInfo - 订单信息对象
 * @param {string} userInfo - 用户标识信息
 * @param {string} conversationId - 会话ID
 * @param {Object} state - 会话状态对象
 * @param {Object} message - 消息对象
 * @returns {Promise<boolean>} - 处理成功返回true，否则返回false
 */
// 全局变量，用于存储最近的订单信息请求
let lastOrderRequestConversationId = null;
let lastOrderRequestUserId = null;
let lastOrderRequestTime = null;
let orderInfoRequestPromise = null;

// 重复请求检测的超时时间（毫秒）
const ORDER_REQUEST_TIMEOUT = 10000; // 10秒

// 手动清除重复检测状态的函数
function clearOrderRequestState() {
    lastOrderRequestConversationId = null;
    lastOrderRequestUserId = null;
    lastOrderRequestTime = null;
    debug('[下单数处理] 手动清除重复检测状态');
}

// 将函数暴露到全局，方便调试
window.clearOrderRequestState = clearOrderRequestState;

// 导入difyAPI模块
let difyAPIModule = null;

// 发送订单信息到DIFY但不发送回复给买家
async function sendDifyOrderInfoAsync(orderInfo, userInfo, conversationId, state, message) {
    debug('[下单数处理] 开始异步发送订单信息到DIFY');
    
    // 创建一个新的Promise来跟踪请求
    orderInfoRequestPromise = new Promise((resolveOrderRequest, rejectOrderRequest) => {
        // 检查是否是重复请求（同一会话且在超时时间内）
        const now = Date.now();
        const isRecentRequest = lastOrderRequestTime && (now - lastOrderRequestTime) < ORDER_REQUEST_TIMEOUT;
        const isSameRequest = lastOrderRequestConversationId === conversationId && lastOrderRequestUserId === userInfo;

        if (isRecentRequest && isSameRequest) {
            debug('[下单数处理] 检测到重复请求，跳过发送');
            debug(`[下单数处理] 上次请求时间: ${new Date(lastOrderRequestTime).toLocaleTimeString()}, 当前时间: ${new Date(now).toLocaleTimeString()}`);
            resolveOrderRequest({ status: 'skipped', message: '跳过重复请求' });
            return;
        }

        // 更新最近的请求信息
        lastOrderRequestConversationId = conversationId;
        lastOrderRequestUserId = userInfo;
        lastOrderRequestTime = now;

        debug('[下单数处理] 开始发送新的订单信息请求');
        
        // 使用统一的格式化函数
        let formattedOrderInfo = '';
        try {
            // 使用标准格式化函数
            formattedOrderInfo = `订单数：${orderInfo.orderCount || '未知'}`;

            if (orderInfo.orderStatus) {
                formattedOrderInfo += `\n订单状态：${orderInfo.orderStatus}`;
            }
            if (orderInfo.goodsName) {
                formattedOrderInfo += `\n商品名：${orderInfo.goodsName}`;
            }
            if (orderInfo.amountValue) {
                formattedOrderInfo += `\n实付金额：${orderInfo.amountValue}`;
            } else if (orderInfo.goodsPrice) {
                formattedOrderInfo += `\n实付金额：${orderInfo.goodsPrice}`;
            }
            if (orderInfo.goodsNum) {
                formattedOrderInfo += `\n数量：${orderInfo.goodsNum}`;
            }
            if (orderInfo.remarkNote) {
                formattedOrderInfo += `\n备注：${orderInfo.remarkNote}`;
            }
            if (orderInfo.orderTime) {
                formattedOrderInfo += `\n下单时间：${orderInfo.orderTime}`;
            }

            debug('[下单数处理] 格式化后的订单信息:', formattedOrderInfo);
        } catch (error) {
            debug('[下单数处理] 格式化订单信息失败:', error);
            formattedOrderInfo = `订单数：${orderInfo.orderCount || '未知'}`;
        }
        
        // 添加上下文信息，注明不需要回复给买家
        const requestContent = `这是一个下单数买家的订单信息:\n${formattedOrderInfo}\n记录这个信息，不需要回复买家。`;

        // 请求ID，用于跟踪
        const requestId = Date.now();

        debug('[下单数处理] 准备发送请求到后台脚本');
        debug('[下单数处理] 请求内容:', requestContent);
        debug('[下单数处理] 用户标识:', userInfo);
        debug('[下单数处理] 请求ID:', requestId);

        // 添加超时处理
        const timeoutId = setTimeout(() => {
            debug('[下单数处理] 请求超时，15秒内未收到响应');
            rejectOrderRequest(new Error('请求超时'));
        }, 15000); // 15秒超时

        // 使用chrome.runtime.sendMessage发送请求到后台脚本
        chrome.runtime.sendMessage({
            type: 'difyRequest',
            data: {
                inputs: {},
                query: requestContent,
                response_mode: "blocking", // 使用非流式响应
                user: userInfo,
                requestId: requestId,
                _isBackgroundOrderInfoRequest: true // 标记为后台订单信息请求
            }
        }, response => {
            clearTimeout(timeoutId); // 清除超时定时器
            debug('[下单数处理] 收到后台脚本响应:', response);

            if (chrome.runtime.lastError) {
                debug('[下单数处理] Chrome runtime错误:', chrome.runtime.lastError);
                rejectOrderRequest(new Error(chrome.runtime.lastError.message));
                return;
            }

            // 请求发送成功
            if (response?.success && response.data) {
                debug('[下单数处理] DIFY API请求成功，响应数据:', response.data);
                resolveOrderRequest({ status: 'success', data: response.data });
                
                // 标记消息为已回复（但实际上没有发送任何回复给用户）
                if (message) {
                    if (message.unrepliedMessages && message.unrepliedMessages.length > 0) {
                        for (const msgElement of message.unrepliedMessages) {
                            markMessageAsReplied(msgElement, conversationId)
                                .catch(err => debug('[下单数处理] 标记消息出错:', err));
                        }
                    } else if (message.element) {
                        markMessageAsReplied(message.element, conversationId)
                            .catch(err => debug('[下单数处理] 标记消息出错:', err));
                    }
                    
                    // 更新会话状态
                    if (state) {
                        state.processedMessages.add(message.id);
                        state.lastProcessedTime = Date.now();
                    }
                    
                    // 显示成功状态
                    floatingBallInstance?.setStatus('success');
                }
            } else {
                debug('[下单数处理] DIFY API请求失败:', response?.error);
                rejectOrderRequest(new Error(response?.error || 'Unknown error'));
                floatingBallInstance?.setStatus('error');
            }
        });
    });
    
    return orderInfoRequestPromise;
}

// 兼容原有接口，但改为使用异步方式并且不回复买家
async function sendDifyOrderInfoRequest(orderInfo, userInfo, conversationId, state, message) {
    try {
        // 调用异步版本并等待完成
        await sendDifyOrderInfoAsync(orderInfo, userInfo, conversationId, state, message);
        return true;
    } catch (error) {
        debug('[下单数处理] 处理订单信息失败:', error);
        floatingBallInstance?.setStatus('error');
        return false;
    }
}

async function difyReplyProcess(r, s, m) {
    // 检查是否是后台商品卡请求或订单信息请求的响应，如果是则跳过处理
    if (r.data && (r.data._isBackgroundProductCard === true || r.data._isBackgroundOrderInfoRequest === true)) {
        debug('检测到后台请求响应，跳过处理');
        return;
    }
    
    //优先检查转人工
    const settings = await StateManager.getState('transferSettings', {
        manualEnabled: true,  // 总开关
        difyTransferEnabled: true // 关键词触发
    });

    //插入dify引导转人工逻辑--modify by rice
    if(r.data.answer.includes("转人工") || r.data.answer.includes("转售前") || r.data.answer.includes("转售后") || r.data.answer.includes("转接")) {
        if(settings.manualEnabled && settings.difyTransferEnabled) {
            await sendMessage('收到，请您稍候一下···');
            return 'transfer';
            
        }else{
            // 获取自定义的转人工失败提示文案
            let transferFailMessage = await chrome.storage.local.get('transferFailMessage')
                .then(res => res.transferFailMessage || '实在抱歉无法转接，请稍候再联系')
                .catch(() => '实在抱歉无法转接，请稍候再联系');
            
            await sendMessage(transferFailMessage + ' ' + getEmojiByType('apology'));
            return;
        }

    }



    try {
        let difyResponseHandlerModule = await import('../utils/handleDifyResponse.js');
        let difyResponseHandler = difyResponseHandlerModule.createDifyResponseHandler();
        
        // 处理 Dify 回复(触发对应操作)
        let answer = await difyResponseHandler.handle(r.data.answer);
        debug('[debug] 触发完成',answer);
        if(answer.status === 'success' ) {
            //成功触发
            if(answer.message ) {
                //发完图片后，发送文本
                debug('发完图片后，发送文本',answer.message);
                await sendMessage(answer.message);
            }
                
            return;
        }else if(answer.status === 'error' ) {
            //触发发生异常
            debug('[debug] 触发发生异常',answer.message);
            return;
        }else  {
        //正常回复
        //添加备注检查及处理
        if(r.data.answer.includes('备注：') ) {
            if(await checkBackupKeywordEnabled()) {
                const parts = r.data.answer.split('备注：');
                let remark = parts[1]?.trim();
                if(remark) {
                    // 检查是否包含✓符号，如果包含则跳过处理（表示已经处理过）
                    if(remark.includes('✓')) {
                        debug('[debug] 备注已处理过，跳过添加');
                        // 发送默认回复文案
                        await sendMessage('好的，已经备注了 ' + getEmojiByType('positive'));
                        r.data.answer = ''; // 清空原回答，避免重复发送
                    } else {
                        let order_item = await findBackupKeywordOrderItem();
                        if(order_item) {
                            await actionBackupKeyword(remark, order_item);
                            r.data.answer = parts[0]?.trim()??'谢谢';
                        }
                    }
                }
            }else {
                //未开启选项
                // 不再发送DIFY请求，使用用户设置的文案
                debug('添加用户备注功能被关闭，使用用户设置的回复文案');
                // 获取用户设置的文案，如果没有则使用默认值
                chrome.storage.local.get('orderNoteReplyMessage').then(res => {
                    const replyText = res.orderNoteReplyMessage || '收到，稍后为您处理';
                    // 添加随机ORDER表情
                    const orderEmoji = getEmojiByType('order');
                    sendMessage(replyText + ' ' + orderEmoji);
                }).catch(error => {
                    sendMessage('收到，稍后为您处理 ' + getEmojiByType('order'));
                });

            }
        }else if( r.data.answer.includes('打标：') ) {
            // 检查回复是否包含"打标："关键词
            debug('[打标] 检测到打标指令', r.data.answer);
            
            if(await checkStarFlagEnabled()) {
                //打星标功能开启，执行打星标
                actionStarFlag();
                const parts = r.data.answer.split('打标：');
                debug('[打标] 打标回复',parts);
                // 只保留消息部分，去掉指令
                let remark = parts[0]?.trim();
                // 如果指令后还有文本，也合并进来
                if(parts[1]) {
                    remark = remark ? remark + " " + parts[1]?.trim() : parts[1]?.trim();
                }
                
                if(remark) {
                    r.data.answer = remark;
                }else {
                    r.data.answer = '谢谢';
                }
            }else {
                // 星标功能关闭，直接发送失败提示语
                debug('添加用户星标功能被关闭，发送星标失败提示语');
                
                // 直接获取用户设置的失败提示文案，不处理原始消息内容
                chrome.storage.local.get('starFlagReplyMessage').then(res => {
                    const replyText = res.starFlagReplyMessage || '抱歉，星标功能暂时不可用';
                    // 添加歉意表情，使用专门为此设计的表情类型
                    const apologyEmoji = getEmojiByType('apology');
                    sendMessage(replyText + ' ' + apologyEmoji);
                }).catch(error => {
                    sendMessage('抱歉，星标功能暂时不可用 ' + getEmojiByType('apology'));
                });
                
                // 阻止后续处理原始消息
                return;
            }
            
        }

        debug('[AI回复] 发送消息',r.data.answer);
        const success = await sendToPddCustomer(r.data.answer);
        if (success) {
            try {
                // 标记消息为已回复 - 使用正确的作用域
                // 检查是否有未回复消息列表
                if (m.unrepliedMessages && m.unrepliedMessages.length > 0) {
                    // 标记所有未回复的消息为已回复
                    debug(`在difyReplyProcess中标记 ${m.unrepliedMessages.length} 条未回复消息为已回复`);
                    for (const msgElement of m.unrepliedMessages) {
                        // 使用saveRepliedMessage直接处理，避免引用不同作用域的函数
                        try {
                            await saveRepliedMessage(s.getPddConversationId(), msgElement.id || (Date.now() + '_' + Math.random().toString(36).substring(2, 8)));
                            // 在消息元素上添加一个标记，表示已经在difyReplyProcess中处理过
                            msgElement._markedAsReplied = true;
                            
                            // 添加视觉标记（绿色勾号）
                            await addVisualReplyMark(msgElement);
                        } catch (markError) {
                            debug('标记单条消息为已回复时出错:', markError);
                        }
                    }
                } else if (m.element) {
                    // 兼容旧逻辑，只标记当前消息
                    await saveRepliedMessage(s.getPddConversationId(), m.id || (Date.now() + '_' + Math.random().toString(36).substring(2, 8)));
                    // 在消息元素上添加一个标记，表示已经在difyReplyProcess中处理过
                    m.element._markedAsReplied = true;
                    
                    // 添加视觉标记（绿色勾号）
                    await addVisualReplyMark(m.element);
                }
            } catch (markError) {
                debug('标记消息为已回复时出错:', markError.message);
            }
            
            if(s) {
                s.processedMessages.add(m.id);
                s.lastProcessedTime = Date.now();
            }
            floatingBallInstance?.setStatus('success');
        } else {
            floatingBallInstance?.setStatus('error');
        }
    }
    } catch(error) {
        debug('[AI API回复处理] 错误:', error);
        floatingBallInstance?.setStatus('error');
    }
    
    // 返回一个标记，表示已经在difyReplyProcess中处理过
    return { messageMarkedAsReplied: true };
}

// 添加视觉回复标记（绿色勾号）
async function addVisualReplyMark(messageElement) {
    if (!messageElement) return;
    
    try {
        // 先获取消息气泡元素
        const msgContent = messageElement.querySelector('.msg-content-box, .text-content');
        if (!msgContent) {
            debug('未找到消息气泡元素');
            return;
        }
        
        // 检查是否已经有标记
        if (msgContent.querySelector('.replied-mark')) {
            debug('消息已有视觉回复标记');
            return;
        }
        
        // 创建标记元素
        const mark = document.createElement('div');
        mark.className = 'replied-mark';
        mark.textContent = '✓';
        mark.style.cssText = `
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            background-color: #4CAF50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            z-index: 100;
            box-shadow: 0 1px 2px rgba(0,0,0,0.2);
            overflow: hidden;
        `;
        
        // 添加到消息气泡元素中
        msgContent.style.position = 'relative';
        msgContent.appendChild(mark);
        debug('已添加视觉回复标记');
    } catch (error) {
        debug('添加视觉回复标记时出错:', error);
    }
}

// 优化产品卡片处理流程，在回复默认消息后立即标记为已回复
async function sendDefaultProductReply(message, conversationId) {
    // 先检查消息是否已经回复过，避免重复回复
    if(message && message.element && conversationId) {
        try {
            const messageId = message.id || generateMessageId(message.element);
            const alreadyReplied = await isMessageReplied(conversationId, messageId);
            if(alreadyReplied) {
                debug('商品卡消息已回复过，跳过重复回复:', messageId);
                return true; // 如果已回复过，视为成功，不重复发送
            }
        } catch(error) {
            debug('检查商品卡回复状态时出错:', error);
            // 出错时继续处理，确保消息得到回复
        }
    }
    
    //获取默认回复文案
    let defaultProductReply = await chrome.storage.local.get('customProductReply');
    debug('[商品卡] 默认回复',defaultProductReply);
    
    let success = false;
    if(defaultProductReply.customProductReply) {
        success = await sendMessage(defaultProductReply.customProductReply +' '+ getEmojiByType('product_card'));
    } else {
        //使用默认文案
        const defaultText = "这款商品有什么需要了解吗";
        success = await sendMessage(defaultText + ' ' + getEmojiByType('product_card'));
    }
    
    // 如果发送成功，立即标记商品卡为已回复
    if(success && message && message.element && conversationId) {
        debug('已发送商品卡默认回复');
        debug('标记 1 条商品卡消息为已回复');
        try {
            // 直接保存到存储，不需要添加DOM标记
            const messageId = message.id || generateMessageId(message.element);
            await saveRepliedMessage(conversationId, messageId);
        } catch (error) {
            debug('标记商品卡消息为已回复时出错:', error);
        }
    }
    
    return success;
}

// 表情列表
const EMOJI_LIST = {
    POSITIVE: ['[嘻嘻]', '[大爱]', '[耶]', '[愉快]', '[好的]', '[点赞]', '[握手]', '[加油]','[亲亲]','[爱心]',],  // 欢迎通用
    PRODUCT: ['[大爱]', '[好的]', '[嘻嘻]'],  // 商品咨询专用
    ORDER: ['[好的]', '[点赞]', '[握手]', '[OK]'],  // 订单相关
    REFUND: ['[OK]', '[握手]', '[好的]'], //退款相关
    PRODUCT_CARD: ['[嘻嘻]', '[大爱]', '[害羞]', '[耶]', '[奋斗]', '[馋]', '[偷看]', '[馋]', '[玫瑰]', '[爱心]', '[小心心]'],  //商品卡相关
    APOLOGY: ['[难过]', '[害羞]', '[委屈]', '[拥抱]', '[握手]', '[小心心]']  //表示歉意相关
};

// 根据消息类型选择合适的表情
function getEmojiByType(type) {
    debug('【表情】', '进入选择表情');
    // 统一转换为小写，以便匹配
    const lowerType = typeof type === 'string' ? type.toLowerCase() : type;
    
    switch (lowerType) {
        case 'image':
            // 看到图片时用积极的表情
            return EMOJI_LIST.POSITIVE[Math.floor(Math.random() * EMOJI_LIST.POSITIVE.length)];
            
        case 'order':
            // 看到订单时用确认类表情
            return EMOJI_LIST.ORDER[Math.floor(Math.random() * EMOJI_LIST.ORDER.length)];
            
        case 'refund':
            // 看到退款时用较正式的表情
            debug('【表情】', '选择退款表情');
            return EMOJI_LIST.REFUND[Math.floor(Math.random() * EMOJI_LIST.REFUND.length)];
            
        case 'product_card':
            // 看到商品卡时用商品专用表情
            return EMOJI_LIST.PRODUCT_CARD[Math.floor(Math.random() * EMOJI_LIST.PRODUCT_CARD.length)];
            
        case 'apology':
            // 需要表示歉意时使用的表情
            return EMOJI_LIST.APOLOGY[Math.floor(Math.random() * EMOJI_LIST.APOLOGY.length)];

        default:
            // 其他情况也用积极表情
            return EMOJI_LIST.POSITIVE[Math.floor(Math.random() * EMOJI_LIST.POSITIVE.length)];
    }
}

// 检查是否要传递多媒体到dify
async function checkDifyMediaEnabled() {
    const aiSettings = await chrome.storage.local.get('aiSettings');
    const transferSettings = await chrome.storage.local.get('transferSettings');
    let aiImageEnabled = false;
    let imageEnabled = false;
    if(aiSettings && aiSettings.aiSettings) {
        aiImageEnabled = aiSettings.aiSettings.aiImageEnabled;    
    }
    if(transferSettings && transferSettings.transferSettings) {
        imageEnabled = transferSettings.transferSettings.imageEnabled;
    }
    if(!imageEnabled && !aiImageEnabled) {
        //都关闭时，才转多媒体给dify
        return true;
    }
    return false;
}

function getTopUsername() {
    // 获取客服账号 - 从页面头部的客服信息中获取
    const serviceAccount = document.querySelector('.header .nickname')?.getAttribute('title') || '';
    
    // 获取当前聊天用户的昵称
    const span_name = document.querySelector('.header-box .base-info .base-info-top .name');
    const nickname = span_name?.textContent || '';
    
    // 获取用户ID - 从当前活跃的聊天项中获取
    const activeChat = document.querySelector('.chat-item-box.active');
    let userId = '';
    if (activeChat) {
        const dataRandom = activeChat.getAttribute('data-random');
        if (dataRandom) {
            userId = extractNumericUserId(dataRandom) || '';
        }
    }
    
    // 检查是否为游客
    if (nickname.includes('游客') || nickname.includes('default_user')) {
        return 'youke';
    }
    
    // 处理昵称，格式化为"1***"这样的格式
    const maskedNickname = nickname ? (nickname.charAt(0) + '***') : '';
    
    // 新的格式：用户ID_客服账号_昵称
    if (userId && serviceAccount && nickname) {
        return `${userId}_${serviceAccount}_${maskedNickname}`;
    } else if (userId && serviceAccount) {
        return `${userId}_${serviceAccount}`;
    } else if (userId && nickname) {
        return `${userId}__${maskedNickname}`;
    } else if (serviceAccount && nickname) {
        return `_${serviceAccount}_${maskedNickname}`;
    } else if (userId) {
        return userId;
    } else if (serviceAccount) {
        return `_${serviceAccount}`;
    } else {
        return maskedNickname || 'unknown';
    }
}

async function checkUsername(userId) {
    if (userId === '游客' || userId === 'default_user') {
        debug('游客替换');
        for (let i = 0; i < 3; i++) {
            const span_name = document.querySelector('.header-box .base-info .base-info-top .name');
            if (span_name) return span_name.textContent;
            await sleep(500); // 使用异步 sleep
        }
        return 'youke';
    }
    return userId;
}
//获取头像
function getLastSegmentFromUrl(url) {
    // 检查输入是否为空
    if (!url) {
      return '[]';
    }
    
    // 移除URL末尾的斜杠(如果有)
    const trimmedUrl = url.endsWith('/') ? url.slice(0, -1) : url;
    
    // 查找最后一个斜杠的位置
    const lastSlashIndex = trimmedUrl.lastIndexOf('/');
    
    // 如果没有找到斜杠，返回整个URL
    if (lastSlashIndex === -1) {
      return '['+ trimmedUrl.slice(-10) + ']';
    }
    
    // 返回最后一个斜杠后的内容
    return '['+ trimmedUrl.substring(lastSlashIndex + 1).slice(-10) + ']';
  }


  function checkpopupreplay() {
    //检查是否有弹窗repeat-interceptor-popup .el-button el-button--default el-button--mini
    let popup = document.querySelector('.repeat-interceptor-popup .el-button--default.el-button--mini');
    if(popup) {
        popup.click();
    }
  }


// 系统健康检查函数
function performHealthCheck() {
    const healthStatus = {
        timestamp: new Date().toLocaleTimeString(),
        isProcessing: isProcessing,
        isProcessingMessage: window.isProcessingMessage,
        jump_listenid_count: jump_listenid_count,
        hasUnreadMessagesTimer: checkUnreadMessages_listenid > 0,
        hasNotificationTimer: checkNotification_listenid > 0,
        hasStatusMonitor: !!window.statusMonitorTimer,
        processingDuration: window.lastProcessingStartTime ? 
            Date.now() - window.lastProcessingStartTime : 0,
        messageProcessingDuration: window.lastMessageProcessingStartTime ? 
            Date.now() - window.lastMessageProcessingStartTime : 0
    };
    
    debug('系统健康检查:', healthStatus);
    
    // 检查是否有异常状态需要修复
    let needsRepair = false;
    const repairs = [];
    
    if (healthStatus.processingDuration > 45000) {
        needsRepair = true;
        repairs.push('处理状态超时');
    }
    
    if (healthStatus.messageProcessingDuration > 90000) {
        needsRepair = true;
        repairs.push('消息处理状态超时');
    }
    
    if (healthStatus.jump_listenid_count > 15) {
        needsRepair = true;
        repairs.push('跳过计数过高');
    }

    // 检查订单请求状态是否需要清理
    if (lastOrderRequestTime) {
        const orderRequestAge = Date.now() - lastOrderRequestTime;
        if (orderRequestAge > ORDER_REQUEST_TIMEOUT) {
            needsRepair = true;
            repairs.push('订单请求状态过期');
        }
    }

    if (needsRepair) {
        debug('检测到系统异常，需要修复:', repairs);
        // 执行修复操作
        isProcessing = false;
        window.isProcessingMessage = false;
        jump_listenid_count = 0;
        window.lastProcessingStartTime = null;
        window.lastMessageProcessingStartTime = null;

        // 清理过期的订单请求状态
        if (lastOrderRequestTime && (Date.now() - lastOrderRequestTime) > ORDER_REQUEST_TIMEOUT) {
            debug('清理过期的订单请求状态');
            lastOrderRequestConversationId = null;
            lastOrderRequestUserId = null;
            lastOrderRequestTime = null;
        }

        debug('系统修复完成');
    }
    
    return healthStatus;
}



  // 每30秒执行一次健康检查（使用原子操作防止竞态条件）
window.createHealthCheckTimer = function() {
    // 使用原子操作确保只创建一次
    if (window.healthCheckTimer) {
        debug('健康检查定时器已存在，跳过');
        return false;
    }
    
    // 如果正在创建，也跳过
    if (window.healthCheckTimerCreating) {
        debug('健康检查定时器正在创建中，跳过');
        // 防止创建标志长时间未清除导致无法创建定时器
        setTimeout(() => {
            if (window.healthCheckTimerCreating && !window.healthCheckTimer) {
                debug('健康检查定时器创建标志长时间未清除，强制重置');
                window.healthCheckTimerCreating = false;
            }
        }, 5000);
        return false;
    }
    
    // 原子设置创建标志
    window.healthCheckTimerCreating = true;
    
    // 使用 setTimeout 确保异步执行，避免竞态条件
    setTimeout(() => {
        try {
            // 最终检查，确保没有其他线程创建了定时器
            if (window.healthCheckTimer) {
                debug('健康检查定时器已被其他线程创建，跳过');
                window.healthCheckTimerCreating = false;
                return;
            }
            
            const healthCheckTimer = setInterval(() => {
                try {
                    // 检查页面是否还在拼多多商家后台
                    const currentUrl = window.location.href;
                    if (!currentUrl.includes('mms.pinduoduo.com/chat-merchant/index.html')) {
                        debug('页面已离开拼多多商家后台，清理健康检查定时器');
                        clearInterval(window.healthCheckTimer);
                        window.healthCheckTimer = null;
                        window.healthCheckTimerCreating = false;
                        return;
                    }
                    
                    // 执行健康检查
                    performHealthCheck();
                } catch (error) {
                    debug('健康检查执行异常:', error);
                    // 如果健康检查本身出现严重异常，清理并重新创建
                    if (error.name === 'TypeError' || error.message.includes('Cannot read property')) {
                        debug('健康检查异常严重，重新创建定时器');
                        clearInterval(window.healthCheckTimer);
                        window.healthCheckTimer = null;
                        window.healthCheckTimerCreating = false;
                        // 延迟重新创建
                        setTimeout(() => {
                            window.createHealthCheckTimer();
                        }, 10000);
                    }
                }
            }, 30000);
            
            window.healthCheckTimer = healthCheckTimer;
            debug('健康检查定时器已启动，ID:', healthCheckTimer);
            
        } finally {
            window.healthCheckTimerCreating = false;
        }
    }, 0);
    
    return true;
}



/**
 * 发送图片到 Dify 处理并返回识别结果
 * @param {string} imageUrl - 图片URL
 * @param {string} userInfo - 用户信息（昵称 + 头像标识）
 * @returns {Promise<string>} - 返回 Dify 的识别结果（文本）
 * @throws {Error} - 如果请求失败或返回无效数据
 */
function sendDifyImageRequest(imageUrl, userInfo) {

    //开启图片识别
    debug('[监听消息] 开启图片识别',imageUrl);
    return new Promise((resolve) => {
        //图片发送给dify处理
        chrome.runtime.sendMessage({
            type: 'difyRequest',
            data: {
                query: "请识别图片内容，并结合上下文回答",
                inputs:{},
                files: [{
                    type: 'image',
                    transfer_method: 'remote_url',
                    url: imageUrl
                }],
                response_mode: "streaming",
                user: userInfo
            }
            
        }, async response => {
            if (chrome.runtime.lastError) {
                debug('Chrome runtime错误:', chrome.runtime.lastError);
                resolve(); // 不再reject，避免触发外层catch
                // 执行清理逻辑
                return;
            }
            debug('[监听消息]收到 Dify 响应:', response);
            if (response?.success) {
                if(!response.data.answer) {
                    sendMessage('抱歉查看不了图片，可以麻烦您描述图片情况吗');
                    resolve();
                }

                const success = await sendToPddCustomer(response.data.answer, true);
                
                debug('[监听消息]AI图片识别成功:');
            } else {
                debug('[监听消息]AI API 响应错误:', response?.error);
                
            }
            resolve();
        });
    });

}



function sendDifyMediaRequest(emoji_content, userInfo) {

    try{
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                type: 'difyRequest',
                data: {
                    inputs: {},
                    query: emoji_content,
                    response_mode: "streaming",
                    user: userInfo
                }
            }, async response => {
                if (chrome.runtime.lastError) {
                    debug('Chrome runtime错误:', chrome.runtime.lastError);
                    reject(new Error(chrome.runtime.lastError.message));
                    // 执行清理逻辑
                    return;
                }


                if (response?.success) {
                    let p_res = await difyReplyProcess(response);
                    if(p_res === 'transfer') {
                        await handleTransfer(pddConversationId);
                    }
                }else {
                    debug('Dify API 响应错误:', response?.error);
                }
                resolve();
                return;
            });
        })
    }catch (error) {
        debug('多媒体处理失败，发送默认表情回复:', error.message);
        // 超时或失败时发送默认表情回复
        sendMessage(getEmojiByType('product_card'));
    }
}


function sendDifyUpdateAddressRequest(userInfo, conversationId, state, message) {

    return new Promise((resolve, reject) => {
        // 使用格式化的用户标识（客服账号+昵称+用户ID）
        const formattedUserInfo = getTopUsername();
        debug('发送给DIFY的用户标识:', formattedUserInfo);
        
        chrome.runtime.sendMessage({
            type: 'difyRequest',
            data: {
                inputs: {},
                query: '[买家申请修改为新地址]消息',
                response_mode: "streaming",
                user: formattedUserInfo
            }
        }, async response => {
            if (chrome.runtime.lastError) {
                debug('Chrome runtime错误:', chrome.runtime.lastError);
                reject(new Error(chrome.runtime.lastError.message));
                // 执行清理逻辑
                return;
            }

            debug('[AI API]收到 Dify 响应:', response);
            if (response?.success) {
                let p_res = await difyReplyProcess(response, state, message);
                if(p_res === 'transfer') {
                    await handleTransfer(conversationId);
                }
            } else {
                debug('Dify API 响应错误:', response?.error);
                floatingBallInstance?.setStatus('error');
            }
            resolve();
            return;
        });
    });
}


function sendDifyTxtRequest(content, userInfo, conversationId, state, message) {

    return new Promise((resolve, reject) => {
        // 检查是否有最近的会话ID可以复用
        let requestData = {
                inputs: {},
                query: content,
                response_mode: "streaming",
                user: userInfo
        };
        
        // 提取当前用户ID
        let currentUserId = userInfo;
        if (userInfo && userInfo.includes('_')) {
            currentUserId = userInfo.split('_')[0];
        }
        
        // 如果有最近的会话ID，并且用户ID匹配，则使用该会话ID
        if (lastConversationId && lastUserId && currentUserId === lastUserId) {
            debug('使用商品卡请求的会话ID:', lastConversationId, '用户ID:', lastUserId);
            requestData.conversation_id = lastConversationId;
        } else {
            debug('没有找到匹配的会话ID，创建新会话');
            // 记录当前情况以便调试
            debug('当前状态 - lastConversationId:', lastConversationId, 
                  'lastUserId:', lastUserId, 
                  'currentUserId:', currentUserId);
        }

        chrome.runtime.sendMessage({
            type: 'difyRequest',
            data: requestData
        }, async response => {
            if (chrome.runtime.lastError) {
                debug('Chrome runtime错误:', chrome.runtime.lastError);
                reject(new Error(chrome.runtime.lastError.message));
                return;
            }
            
            try {
                debug('[AI API]收到响应:', response);
                if (response?.success) {
                    // 保存会话ID供后续使用
                    if (response.data && response.data.conversation_id) {
                        lastConversationId = response.data.conversation_id;
                        lastUserId = currentUserId;
                        debug('更新文本消息会话ID:', lastConversationId);
                    }
                    
                    let p_res = await difyReplyProcess(response, state, message);
                    if(p_res === 'transfer') {
                        await handleTransfer(conversationId);
                    }
                    
                    // 检查是否已经在difyReplyProcess中标记为已回复
                    const messageMarkedInDifyProcess = p_res && p_res.messageMarkedAsReplied === true;
                    response.messageMarkedInDifyProcess = messageMarkedInDifyProcess;
                } else {
                    debug('Dify API 响应错误:', response?.error);
                    floatingBallInstance?.setStatus('error');
                }
                resolve(response);
            } catch(error) {
                debug('Dify API 响应错误:', error);
                reject(error);
            }
        });
    });
}


function sendDifyProductRequest(content, userInfo, conversationId, state, message) {
    return new Promise((resolve, reject) => {
        // const timeoutId = setTimeout(() => {
        //     reject(new Error('商品消息处理超时'));
        // }, 10000);

        chrome.runtime.sendMessage({
            type: 'difyRequest',
            data: {
                inputs: {},
                query: content,
                response_mode: "streaming",
                user: userInfo
            }
        }, async response => {
            //clearTimeout(timeoutId);
            
            if (chrome.runtime.lastError) {
                debug('Chrome runtime错误:', chrome.runtime.lastError);
                reject(new Error(chrome.runtime.lastError.message));
                return;
            }
            
            if (response?.success) {
                let p_res = await difyReplyProcess(response, state, message);
                if(p_res === 'transfer') {
                    await handleTransfer(conversationId);
                }
            } else {
                debug('Dify API 响应错误:', response?.error);
            }
            resolve(response);
            return;
        });
    });
}

// 转人工账号管理函数
async function loadServiceAccounts() {
    return new Promise(resolve => {
        chrome.storage.local.get(['serviceAccounts'], result => {
            const accounts = result.serviceAccounts || [];
            debug('[转人工账号] 加载账号列表:', accounts);
            resolve(accounts);
        });
    });
}

async function saveServiceAccounts(accounts) {
    return new Promise(resolve => {
        chrome.storage.local.set({ serviceAccounts: accounts }, () => {
            debug('[转人工账号] 保存账号列表:', accounts);
            resolve(true);
        });
    });
}

async function addServiceAccount() {
    let account = document.getElementById('targetServiceAccount').value;
    if (!account || typeof account !== 'string') {
        debug('[转人工账号] 无效的账号');
        return false;
    }

    account = account.trim();
    if (account.length === 0) {
        debug('[转人工账号] 账号不能为空');
        return false;
    }

    const accounts = await loadServiceAccounts();
    if (accounts.includes(account)) {
        debug('[转人工账号] 账号已存在:', account);
        return false;
    }

    accounts.push(account);
    await saveServiceAccounts(accounts);
    debug('[转人工账号] 添加账号成功:', account);
    //重置输入框
    document.getElementById('targetServiceAccount').value = "";
    await loadServiceAccountList(); // 重新加载账号列表
    return true;
}

async function removeServiceAccount(account) {
    const accounts = await loadServiceAccounts();
    const index = accounts.indexOf(account);
    if (index > -1) {
        accounts.splice(index, 1);
        await saveServiceAccounts(accounts);
        debug('[转人工账号] 删除账号成功:', account);
        return true;
    }
    return false;
}

async function loadServiceAccountList() {
    const accountList = document.getElementById('serviceAccountList');
    if (!accountList) return;

    const accounts = await loadServiceAccounts();
    
    accountList.innerHTML = accounts.map(account => `
        <div class="keyword-item">
            <span>${account}</span>
            <button class="delete-account" data-account="${account}">×</button>
        </div>
    `).join('');

    // 添加删除事件监听
    accountList.querySelectorAll('.delete-account').forEach(button => {
        button.onclick = async () => {
            const account = button.dataset.account;
            if (account) {
                const success = await removeServiceAccount(account);
                if (success) {
                    loadServiceAccountList(); // 重新加载账号列表
                }
            }
        };
    });
}

// 查找客服列表中的"转移"按钮
async function findTransferItemButton() {
    debug('[转人工] 开始查找客服列表中的"转移"按钮');
    
    // 等待按钮出现
    for (let i = 0; i < 10; i++) {
        try {
            // 查找所有可能的"转移"按钮 - 扩展选择器范围
            const transferButtons = document.querySelectorAll('.item-btn-transfer, [data-v-309797d7].item-btn-transfer, div.item-btn-transfer, div[class*="transfer"], .el-button--primary, button:contains("转移"), .el-button:contains("转移")');
            
            for (const button of transferButtons) {
                // 检查按钮是否可见且包含"转移"文本
                if (button && 
                    button.offsetParent !== null && 
                    !button.disabled &&
                    button.textContent && 
                    button.textContent.includes('转移')) {
                    
                    debug('[转人工] 找到客服列表中的"转移"按钮:', button.outerHTML);
                    return button;
                }
            }
            
            // 尝试更具体的选择器
            const specificButton = document.querySelector('div[data-v-309797d7].item-btn-transfer');
            if (specificButton && specificButton.offsetParent !== null) {
                debug('[转人工] 找到客服列表中的"转移"按钮(具体选择器):', specificButton.outerHTML);
                return specificButton;
            }
            
            // 尝试通过XPath查找
            try {
                const xpaths = [
                    "//div[contains(@class, 'item-btn-transfer')]/span[contains(text(), '转移')]",
                    "//button[contains(text(), '转移')]",
                    "//span[contains(text(), '转移')]/ancestor::button",
                    "//span[contains(text(), '转移')]/parent::*"
                ];
                
                for (const xpath of xpaths) {
                    const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                    if (result.singleNodeValue) {
                        const element = result.singleNodeValue;
                        const button = element.tagName === 'SPAN' ? element.parentNode : element;
                        debug('[转人工] 通过XPath找到"转移"按钮:', button.outerHTML);
                        return button;
                    }
                }
            } catch (xpathError) {
                debug('[转人工] XPath查找出错:', xpathError);
            }
            
            // 尝试查找所有按钮并检查文本内容
            const allButtons = document.querySelectorAll('button, .el-button, [role="button"]');
            for (const button of allButtons) {
                if (button && 
                    button.offsetParent !== null && 
                    !button.disabled &&
                    button.textContent && 
                    button.textContent.includes('转移')) {
                    
                    debug('[转人工] 通过文本内容找到"转移"按钮:', button.outerHTML);
                    return button;
                }
            }
            
        } catch (error) {
            debug('[转人工] 查找"转移"按钮时出错:', error);
        }
        await sleep(300);
    }
    
    debug('[转人工] 未找到客服列表中的"转移"按钮');
    return null;
}

// 从修改地址卡片中提取商品信息
function extractCardInfo(card) {
    try {
        // 获取商品名称
        const goodsNameElement = card.querySelector('.mskefu_aladdin_appx_canvas__goodsName___tFHJU');
        const goodsName = goodsNameElement ? goodsNameElement.textContent.trim() : '';
        
        // 获取实收金额
        const priceElement = card.querySelector('.mskefu_aladdin_appx_canvas__priceContainer___ik8x0 div:last-child');
        const priceText = priceElement ? priceElement.textContent.trim() : '';
        const price = priceText.replace('￥', '').trim();
        
        debug('[改地址] 提取卡片信息: 商品名称:', goodsName, '实收金额:', price);
        
        return {
            goodsName,
            price
        };
    } catch (error) {
        debug('[改地址] 提取卡片信息失败:', error);
        return null;
    }
}

// 查找匹配的订单并获取状态
async function findMatchingOrderStatus(cardInfo) {
    if (!cardInfo || !cardInfo.goodsName || !cardInfo.price) {
        debug('[改地址] 卡片信息不完整，无法查找订单');
        return -1;
    }
    
    debug('[改地址] 开始查找订单，商品:', cardInfo.goodsName, '价格:', cardInfo.price);
    
    // 点击"最新订单"标签
    const orderTabElements = document.querySelectorAll('.bar-box.four-tab .bar-item');
    let orderTabClicked = false;
    
    for (const tab of orderTabElements) {
        if (tab.textContent.trim() === '最新订单') {
            tab.click();
            orderTabClicked = true;
            break;
        }
    }
    
    if (!orderTabClicked) {
        debug('[改地址] 未找到"最新订单"标签');
        return -1;
    }
    
    // 等待并点击"个人订单"标签
    if (!(await waitForElementOrTimeout('.order-panel-second-bar', 5000))) {
        debug('[改地址] 等待"个人订单"标签超时');
        return -1;
    }
    
    const personalOrderTabs = document.querySelectorAll('.order-panel-second-bar');
    let personalOrderClicked = false;
    
    for (const tab of personalOrderTabs) {
        if (tab.textContent.trim() === '个人订单') {
            tab.click();
            personalOrderClicked = true;
            break;
        }
    }
    
    if (!personalOrderClicked) {
        debug('[改地址] 未找到"个人订单"标签');
        return -1;
    }
    
    // 等待订单列表加载
    if (!(await waitForElementOrTimeout('.order-item-list .order-item', 5000))) {
        debug('[改地址] 等待订单列表超时');
        return -1;
    }
    
    // 查找匹配的订单
    const orderItems = document.querySelectorAll('.order-item-list .order-item');
    let matchedOrder = null;
    
    for (const orderItem of orderItems) {
        // 获取订单中的商品名称
        const orderGoodsName = orderItem.querySelector('.goods-name')?.textContent.trim();
        
        // 获取订单中的实收金额
        const orderPriceElement = orderItem.querySelector('.amount-value');
        const orderPrice = orderPriceElement ? 
            orderPriceElement.textContent.trim().replace('¥', '').trim() : '';
        
        debug('[改地址] 检查订单:', orderGoodsName, orderPrice);
        
        // 比较商品名称和价格
        if (orderGoodsName === cardInfo.goodsName && 
            orderPrice === cardInfo.price) {
            matchedOrder = orderItem;
            debug('[改地址] 找到匹配订单');
            break;
        }
    }
    
    if (!matchedOrder) {
        debug('[改地址] 未找到匹配的订单');
        return -1;
    }
    
    // 获取订单状态
    const statusElement = matchedOrder.querySelector('.order-status .title-status');
    if (!statusElement) {
        debug('[改地址] 未找到订单状态元素');
        return -1;
    }
    
    const statusText = statusElement.textContent.trim();
    debug('[改地址] 订单状态文本:', statusText);
    
    // 解析订单状态 - 简化为待发货和其他两类
    if (statusText.includes('待发货')) {
        return 1; // 待发货
    } else {
        return 2; // 其他状态(已发货、退款、取消等)
    }
}

// 存储已回复消息
async function saveRepliedMessage(conversationId, messageId) {
    try {
        // 获取当前存储的已回复消息
        const result = await chrome.storage.local.get('repliedMessages');
        const repliedMessages = result.repliedMessages || {};
        
        // 确保会话ID的记录存在
        if (!repliedMessages[conversationId]) {
            repliedMessages[conversationId] = {};
        }
        
        // 添加消息ID和时间戳
        repliedMessages[conversationId][messageId] = {
            timestamp: Date.now()
        };
        
        // 存储回Chrome Storage
        await chrome.storage.local.set({ repliedMessages });
        debug('已存储回复记录:', conversationId, messageId);
        
        // 清理过期记录
        cleanupOldReplies();
    } catch (error) {
        debug('存储回复记录失败:', error);
    }
}

// 检查消息是否已回复
async function isMessageReplied(conversationId, messageId) {
    try {
        const result = await chrome.storage.local.get('repliedMessages');
        const repliedMessages = result.repliedMessages || {};
        
        return !!(
            repliedMessages[conversationId] && 
            repliedMessages[conversationId][messageId]
        );
    } catch (error) {
        debug('检查回复记录失败:', error);
        return false;
    }
}

// 清理过期的回复记录
async function cleanupOldReplies() {
    try {
        const MAX_AGE_DAYS = 7; // 保留7天的记录
        const now = Date.now();
        const maxAge = MAX_AGE_DAYS * 24 * 60 * 60 * 1000;
        
        const result = await chrome.storage.local.get('repliedMessages');
        const repliedMessages = result.repliedMessages || {};
        let hasChanges = false;
        
        // 遍历所有会话
        for (const conversationId in repliedMessages) {
            const messages = repliedMessages[conversationId];
            
            // 遍历会话中的所有消息
            for (const messageId in messages) {
                const message = messages[messageId];
                
                // 如果消息超过最大保存时间，删除它
                if (now - message.timestamp > maxAge) {
                    delete messages[messageId];
                    hasChanges = true;
                }
            }
            
            // 如果会话没有消息了，删除会话
            if (Object.keys(messages).length === 0) {
                delete repliedMessages[conversationId];
                hasChanges = true;
            }
        }
        
        // 如果有变化，保存回存储
        if (hasChanges) {
            await chrome.storage.local.set({ repliedMessages });
            debug('已清理过期回复记录');
        }
    } catch (error) {
        debug('清理过期回复记录失败:', error);
    }
}

// 监控存储空间使用情况
async function monitorStorageUsage() {
    try {
        const bytesInUse = await chrome.storage.local.getBytesInUse('repliedMessages');
        const totalBytes = chrome.storage.local.QUOTA_BYTES || 5242880; // 5MB
        const usagePercentage = (bytesInUse / totalBytes) * 100;
        
        debug('存储空间使用情况:', bytesInUse, 'bytes,', usagePercentage.toFixed(2) + '%');
        
        // 如果使用超过80%，执行更激进的清理
        if (usagePercentage > 80) {
            await aggressiveCleanup();
        }
    } catch (error) {
        debug('监控存储空间失败:', error);
    }
}

// 更激进的清理策略
async function aggressiveCleanup() {
    try {
        const MAX_AGE_DAYS = 3; // 更短的保留期
        const now = Date.now();
        const maxAge = MAX_AGE_DAYS * 24 * 60 * 60 * 1000;
        
        const result = await chrome.storage.local.get('repliedMessages');
        const repliedMessages = result.repliedMessages || {};
        let hasChanges = false;
        
        // 遍历所有会话
        for (const conversationId in repliedMessages) {
            const messages = repliedMessages[conversationId];
            
            // 遍历会话中的所有消息
            for (const messageId in messages) {
                const message = messages[messageId];
                
                // 如果消息超过最大保存时间，删除它
                if (now - message.timestamp > maxAge) {
                    delete messages[messageId];
                    hasChanges = true;
                }
            }
            
            // 如果会话没有消息了，删除会话
            if (Object.keys(messages).length === 0) {
                delete repliedMessages[conversationId];
                hasChanges = true;
            }
        }
        
        // 如果有变化，保存回存储
        if (hasChanges) {
            await chrome.storage.local.set({ repliedMessages });
            debug('已执行激进清理');
        }
    } catch (error) {
        debug('激进清理失败:', error);
    }
}

// 初始化已回复消息存储
async function initRepliedMessagesStorage() {
    try {
        // 检查是否已有存储
        const result = await chrome.storage.local.get('repliedMessages');
        if (!result.repliedMessages) {
            // 初始化存储
            await chrome.storage.local.set({ repliedMessages: {} });
            debug('已初始化回复消息存储');
        } else {
            // 已有存储，执行清理
            await cleanupOldReplies();
        }
    } catch (error) {
        debug('初始化回复消息存储失败:', error);
    }
}

// 刷新页面函数
function refreshPage() {
    debug('准备刷新页面...');
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

// 存储已处理的等待时间状态
const processedWaitingStates = {
    waitingTimers: {},
    isProcessing: false,
    
    // 检查会话是否已处理特定等待时间
    hasProcessed(conversationId, minutes) {
        const key = `${conversationId}_${minutes}`;
        return !!this.waitingTimers[key];
    },
    
    // 标记会话的等待时间已处理
    markProcessed(conversationId, minutes) {
        const key = `${conversationId}_${minutes}`;
        this.waitingTimers[key] = Date.now();
        // 5分钟后自动清除记录，允许再次处理
        setTimeout(() => {
            delete this.waitingTimers[key];
            debug(`[等待检测] 重置会话 ${conversationId} 的 ${minutes} 分钟等待状态`);
        }, 5 * 60 * 1000); // 5分钟
    },
    
    // 清理过期记录
    cleanup() {
        const now = Date.now();
        const expiryTime = 5 * 60 * 1000; // 5分钟
        for (const key in this.waitingTimers) {
            if (now - this.waitingTimers[key] > expiryTime) {
                delete this.waitingTimers[key];
            }
        }
    }
};

// 检查等待时间函数
function checkWaitingTime() {
    try {
        // 防止多个等待检测同时执行
        if (processedWaitingStates.isProcessing) {
            debug('[等待检测] 另一个等待检测正在进行中，跳过');
            return false;
        }
        
        processedWaitingStates.isProcessing = true;
        
        // 检查是否有"已等待X分钟"的提示
        const waitingElements = document.querySelectorAll('.chat-unreply-over-time');
        
        if (waitingElements && waitingElements.length > 0) {
            for (const element of waitingElements) {
                const waitText = element.textContent || '';
                debug('[等待检测] 发现等待提示:', waitText);
                
                // 提取等待时间数字
                const timeMatch = waitText.match(/已等待(\d+)(?:分钟|小时)/);
                if (timeMatch) {
                    const waitTime = parseInt(timeMatch[1]);
                    const isHour = waitText.includes('小时');
                    
                    // 转换为分钟
                    const waitMinutes = isHour ? waitTime * 60 : waitTime;
                    
                    debug(`[等待检测] 等待时间: ${waitMinutes}分钟`);
                    
                        // 获取会话ID
                        const conversationElement = element.closest('li.chat-item');
                    if (!conversationElement) continue;
                    
                            const conversationId = getConversationId(conversationElement);
                    if (!conversationId) continue;
                    
                    // 处理等待3分钟的情况
                    if (waitMinutes === 3) {
                        // 检查是否已处理过此会话的3分钟等待
                        if (processedWaitingStates.hasProcessed(conversationId, 3)) {
                            debug(`[等待检测] 会话 ${conversationId} 的3分钟等待已处理过，跳过`);
                            continue;
                        }
                        
                        debug('[等待检测] 检测到等待3分钟，准备转人工');
                                debug('[等待检测] 开始转人工, 会话ID:', conversationId);
                        
                        // 标记为已处理
                        processedWaitingStates.markProcessed(conversationId, 3);
                        
                        // 开始转人工
                                handleTransfer(conversationId);
                        processedWaitingStates.isProcessing = false;
                                return true;
                            }
                    
                    // 处理精确等于5分钟的情况，执行健康检查
                    if (waitMinutes === 5) {
                        // 检查是否已处理过此会话的5分钟等待
                        if (processedWaitingStates.hasProcessed(conversationId, 5)) {
                            debug(`[等待检测] 会话 ${conversationId} 的5分钟等待已处理过，跳过`);
                            continue;
                        }
                        
                        debug('[等待检测] 检测到等待时间精确为5分钟，执行健康检查');
                        
                        // 标记为已处理
                        processedWaitingStates.markProcessed(conversationId, 5);
                        
                        performHealthCheck();
                        processedWaitingStates.isProcessing = false;
                        return true;
                    }
                    
                    // 处理精确等于10分钟的情况，重启自动回复并刷新页面
                    if (waitMinutes === 10) {
                        // 检查是否已处理过此会话的10分钟等待
                        if (processedWaitingStates.hasProcessed(conversationId, 10)) {
                            debug(`[等待检测] 会话 ${conversationId} 的10分钟等待已处理过，跳过`);
                            continue;
                        }
                        
                        debug('[等待检测] 检测到等待时间精确为10分钟，准备重启自动回复并刷新页面');
                        
                        // 标记为已处理
                        processedWaitingStates.markProcessed(conversationId, 10);
                        
                        // 获取FloatingBall实例
                        if (window.floatingBall) {
                            // 关闭自动回复
                            window.floatingBall.toggleAutoReply(false).then(() => {
                                debug('[等待检测] 自动回复已关闭');
                                
                                // 延迟1秒后重新开启
                                setTimeout(() => {
                                    window.floatingBall.toggleAutoReply(true).then(() => {
                                        debug('[等待检测] 自动回复已重新开启');
                                        
                                        // 再延迟1秒后刷新页面
                                        setTimeout(() => {
                                            debug('[等待检测] 准备刷新页面');
                                            refreshPage();
                                        }, 1000);
                                    });
                                }, 1000);
                            });
                        } else {
                            // 如果没有找到FloatingBall实例，直接刷新页面
                            debug('[等待检测] 未找到FloatingBall实例，直接刷新页面');
                            refreshPage();
                        }
                        processedWaitingStates.isProcessing = false;
                        return true;
                    }
                }
            }
        }
        
        // 清理过期的处理记录
        processedWaitingStates.cleanup();
        processedWaitingStates.isProcessing = false;
    } catch (error) {
        debug('[等待检测] 检查等待时间时出错:', error);
        processedWaitingStates.isProcessing = false;
    }
    
    return false;
}

// 发送商品信息到DIFY但不等待响应的异步函数 - 防止重复处理
// 全局变量，用于存储最近的会话ID
let lastConversationId = null;
let lastUserId = null;

// 添加一个Promise来跟踪商品卡请求状态
let productCardRequestPromise = null;

function sendDifyProductRequestAsync(content, userInfo, conversationId, state, message) {
    debug('开始异步发送商品卡信息到DIFY');
    
    // 创建一个新的Promise来跟踪请求
    productCardRequestPromise = new Promise((resolveProductRequest, rejectProductRequest) => {
        // 先检查消息是否已经处理过，避免重复发送
        if (message && message.element && conversationId) {
            try {
                const messageId = message.id || generateMessageId(message.element);
                // 先检查是否已经回复过
                isMessageReplied(conversationId, messageId).then(alreadyReplied => {
                    if (alreadyReplied) {
                        debug('商品卡消息已处理过，跳过API请求:', messageId);
                        resolveProductRequest({ alreadyProcessed: true }); // 如果已处理过，直接返回
                    } else {
                        // 如果没有处理过，继续发送请求
                        sendDifyProductRequest();
                        
                        // 标记为已处理，避免重复发送
                        saveRepliedMessage(conversationId, messageId).then(() => {
                            debug('已标记商品卡消息为已处理:', messageId);
                        });
                    }
                });
            } catch (error) {
                debug('检查商品卡消息状态时出错:', error);
                // 出错时也继续发送请求，保证功能可用
                sendDifyProductRequest();
            }
        } else {
            // 如果没有消息元素或会话ID，直接发送请求
            sendDifyProductRequest();
        }
        
        // 实际发送DIFY请求的函数
        function sendDifyProductRequest() {
            // 设置一个标记，表明这是商品卡的请求
    const requestId = Date.now();
    
            // 提取用户ID用于会话跟踪
            let userId = userInfo;
            if (userInfo && userInfo.includes('_')) {
                userId = userInfo.split('_')[0];
                debug('提取商品卡请求用户ID:', userId);
                // 保存最近的用户ID，用于后续请求
                lastUserId = userId;
            }
            
    chrome.runtime.sendMessage({
        type: 'difyRequest',
        data: {
            inputs: {},
            query: content,
            response_mode: "streaming",
            user: userInfo,
            requestId: requestId
        }
    }, response => {
        if (chrome.runtime.lastError) {
            debug('Chrome runtime错误:', chrome.runtime.lastError);
                    rejectProductRequest(new Error(chrome.runtime.lastError.message));
            return;
        }
        
        // 请求发送成功，记录日志
        debug('商品卡AI API请求已发送');
        
                // 保存会话ID以便后续请求使用
                if (response?.success && response.data) {
                    debug('商品卡AI API请求成功');
                    
                    // 保存会话ID
                    if (response.data.conversation_id) {
                        debug('保存商品卡会话ID:', response.data.conversation_id);
                        lastConversationId = response.data.conversation_id;
                        
                        // 解析Promise，表示已获取到会话ID
                        resolveProductRequest({
                            success: true,
                            conversation_id: response.data.conversation_id,
                            userId: userId
                        });
                    } else {
                        resolveProductRequest({ success: true });
                    }
            
            // 仅记录响应，不触发任何处理
                    if (response.data.answer) {
                        debug(`[${requestId}] 收到商品卡DIFY响应: ` + response.data.answer.substring(0, 30) + '...');
            }
        } else {
            debug('商品卡AI API请求失败:', response?.error);
                    rejectProductRequest(new Error(response?.error || 'Unknown error'));
                }
            });
        }
    });
    
    // 立即返回，不等待响应
    debug('商品卡AI API请求已在后台发送，但已创建Promise跟踪');
    return productCardRequestPromise;
}

// 处理已关闭自动转人工的情况
async function handleTransferClosed() {
    debug('[转人工] 处理已关闭自动转人工的情况');
    // 获取已关闭自动转人工处理方式设置
    const failSettings = await StateManager.getState('transferSettings', {});

    // 获取是否需要发送消息
    const shouldSendMessage = failSettings.transferFailOptions ? 
        failSettings.transferFailOptions.includes('message') : true;

    // 获取是否需要标记星标
    const shouldAddStar = failSettings.transferFailOptions ? 
        failSettings.transferFailOptions.includes('star') : false;

    // 发送提示消息
    if (shouldSendMessage) {
        // 获取道歉表情
        const emoji = getEmojiByType('apology');
        const message = (failSettings.transferFailMessage || '实在抱歉无法转接，请稍候再联系') + ' ' + emoji;
        await sendMessage(message);
    }
    
    // 添加星标
    if (shouldAddStar) {
        try {
            debug('[转人工] 已关闭自动转人工，执行打星标操作');
            actionStarFlag();
        } catch (starError) {
            debug('[转人工] 打星标失败:', starError);
        }
    }
    
    // 关闭搜索框
    let clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true, 
        view: window,
        clientX: 10,
        clientY: 10
    });
    
    let tranform_parent = document.querySelector('.tranform-chat-dialog')?.parentElement;
    if(tranform_parent) {
        tranform_parent.dispatchEvent(clickEvent);
    }
    
    // 消息已处理，不需要再发送到 Dify
    return false;
}

// 处理转人工失败的情况
async function handleTransferFailed() {
    debug('[转人工] 处理转人工失败的情况');
    // 获取转人工失败处理方式设置
    const failSettings = await StateManager.getState('transferSettings', {});

    // 获取是否需要发送消息
    const shouldSendMessage = failSettings.transferErrorOptions ? 
        failSettings.transferErrorOptions.includes('message') : true;

    // 获取是否需要标记星标
    const shouldAddStar = failSettings.transferErrorOptions ? 
        failSettings.transferErrorOptions.includes('star') : true;

    // 发送提示消息
    if (shouldSendMessage) {
        // 获取道歉表情
        const emoji = getEmojiByType('apology');
        const message = (failSettings.transferErrorMessage || '抱歉客服暂时离开，稍后为您处理') + ' ' + emoji;
        await sendMessage(message);
    }
    
    // 添加星标
    if (shouldAddStar) {
        try {
            debug('[转人工] 转人工失败，执行打星标操作');
            actionStarFlag();
        } catch (starError) {
            debug('[转人工] 打星标失败:', starError);
        }
    }
    
    // 关闭搜索框
    let clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true, 
        view: window,
        clientX: 10,
        clientY: 10
    });
    
    let tranform_parent = document.querySelector('.tranform-chat-dialog')?.parentElement;
    if(tranform_parent) {
        tranform_parent.dispatchEvent(clickEvent);
    }
    
    return false;
}