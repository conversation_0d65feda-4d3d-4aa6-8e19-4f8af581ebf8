# DIFY请求问题诊断

## 问题现象

从日志可以看出：
1. ✅ 订单信息成功提取
2. ✅ 订单信息成功格式化
3. ❌ 没有看到后续的DIFY请求发送日志
4. ❌ 没有收到DIFY响应

## 可能的原因

### 1. DIFY配置问题
- API Key 未配置或无效
- API URL 配置错误
- 网络连接问题

### 2. 后台脚本问题
- 后台脚本未正常运行
- 消息传递失败
- 权限问题

### 3. 代码逻辑问题
- 异常处理中断了流程
- Promise 未正确处理
- 回调函数未执行

## 诊断步骤

### 步骤1：检查DIFY配置

在浏览器控制台运行测试脚本：
```javascript
// 复制 test-dify-config.js 的内容并执行
```

**预期结果：**
```
✅ API URL 已配置
✅ API Key 已配置
✅ DIFY请求成功
✅ 收到AI回复
```

### 步骤2：检查后台脚本日志

1. 打开Chrome扩展程序管理页面
2. 找到AIPDD扩展程序
3. 点击"检查视图"中的"背景页"
4. 查看控制台日志

**应该看到的日志：**
```
[timestamp] 处理DIFY请求
[timestamp] 请求数据: {...}
[timestamp] 开始调用 Dify API
[timestamp] 发送请求到: https://api.dify.ai/v1/chat-messages
[timestamp] 收到响应: {status: 200, ok: true}
[timestamp] DIFY请求已完成，发送响应
```

### 步骤3：检查网络请求

在后台脚本控制台的Network标签中：
1. 查看是否有到DIFY API的请求
2. 检查请求状态码
3. 查看响应内容

## 修复方案

### 方案1：DIFY配置问题

如果DIFY配置有问题：

1. **检查API Key**：
   - 确保API Key正确且有效
   - 检查是否有权限限制

2. **检查API URL**：
   - 默认应该是：`https://api.dify.ai/v1`
   - 如果是私有部署，确保URL正确

3. **重新配置**：
   ```javascript
   // 在控制台中重新设置配置
   chrome.runtime.sendMessage({
       type: 'updateDifyConfig',
       data: {
           apiUrl: 'https://api.dify.ai/v1',
           apiKey: 'your-api-key-here'
       }
   });
   ```

### 方案2：后台脚本问题

如果后台脚本有问题：

1. **重新加载扩展程序**
2. **检查manifest.json权限**
3. **查看后台脚本错误日志**

### 方案3：代码逻辑问题

如果代码逻辑有问题，已经在代码中添加了更多调试信息：

**content.js 中的新调试信息：**
```javascript
debug('[下单数处理] 准备发送请求到后台脚本');
debug('[下单数处理] 请求内容:', requestContent);
debug('[下单数处理] 用户标识:', userInfo);
debug('[下单数处理] 请求ID:', requestId);
debug('[下单数处理] 收到后台脚本响应:', response);
```

**background.js 中的新调试信息：**
```javascript
debug(`[${requestId}] 请求数据:`, request.data);
debug(`[${requestId}] 当前DIFY配置:`, {...});
debug('开始调用 Dify API');
debug('发送请求到:', `${currentDifyConfig.apiUrl}/chat-messages`);
debug('收到响应:', {status, statusText, ok});
```

## 测试方法

### 方法1：使用测试脚本

在拼多多商家后台的控制台中运行：
```javascript
// 复制 test-dify-config.js 的内容
```

### 方法2：手动测试

1. **测试配置获取**：
   ```javascript
   chrome.runtime.sendMessage({type: 'getInitialDifyConfig'}, console.log);
   ```

2. **测试简单请求**：
   ```javascript
   chrome.runtime.sendMessage({
       type: 'difyRequest',
       data: {
           inputs: {},
           query: '测试消息',
           response_mode: "blocking",
           user: 'test_user',
           requestId: Date.now()
       }
   }, console.log);
   ```

### 方法3：查看详细日志

重新触发下单数检测，观察完整的日志流程：

1. 重新加载扩展程序
2. 在有订单的买家对话中触发检测
3. 同时查看content.js和background.js的日志
4. 对比日志找出中断点

## 常见问题解决

### 问题1：API Key无效
**现象**：后台脚本报错"Unauthorized"
**解决**：重新获取有效的DIFY API Key

### 问题2：网络连接问题
**现象**：请求超时或连接失败
**解决**：检查网络连接，确认DIFY服务可访问

### 问题3：权限问题
**现象**：chrome.runtime.sendMessage失败
**解决**：检查manifest.json中的权限配置

### 问题4：后台脚本未运行
**现象**：无法找到后台页面
**解决**：重新加载扩展程序，检查manifest.json配置

## 下一步行动

1. **立即执行**：运行测试脚本诊断问题
2. **查看日志**：检查后台脚本控制台
3. **验证配置**：确认DIFY API配置正确
4. **测试网络**：确认可以访问DIFY API
5. **重新测试**：在修复后重新测试订单检测功能
