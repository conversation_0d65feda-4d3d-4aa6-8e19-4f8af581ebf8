

// 编译后的JavaScript版本
class MessageBoxHandler {
    constructor() {
        this.retryCount = 0;
        this.maxRetries = 3;
        this.retryInterval = 5000;

        // 从localStorage获取转人工场景配置
        this.transferScenes = JSON.parse(localStorage.getItem('transferScenes')) || {
            refund: true,          // 退款/售后
            platformIntervene: true,  // 平台介入
            workOrder: true,       // 工单创建
            monitoring: true,      // 监控警告
            complaint: true,       // 投诉处理
            highValue: true,       // 高价值订单
        };

        this.characteristics = {
            blocking: ['forbidden', 'cannot', '无法', '禁止'],
            warning: ['warning', '警告', '提醒'],
            confirmation: ['confirm', '确认', '是否'],
            network: ['网络出现问题', '网络异常', '连接失败'],
            login: ['账户在别处登录', '重新登录', '登录失效'],
            refund: ['退款申请', '售后申请', '退货退款'],
            platformIntervene: ['平台已接管', '平台介入', '平台客服'],
            workOrder: ['工单', '已创建', '跟进处理'],
            monitoring: ['监控中', '监管', '服务监控'],
            complaint: ['投诉', '纠纷', '违规'],
            highValue: ['高价值', '大额', '重要订单'],
            serviceScore: ['service-score-warning-dialog']
        };

        // 先定义所有处理方法
        this.handlePlatformTakeover = async (element) => {
            //console.log('处理平台接管消息框');
            this.closeMessageBox(element);
        };

        this.handleRefundRequest = async (element) => {
            //console.log('处理退款请求消息框');
            if (this.shouldTransferToHuman('refund')) {
                await this.transferToHuman('退款/退货问题');
            } else {
                this.closeMessageBox(element);
            }
        };

        this.handleRepeatWarning = async (element) => {
            //console.log('处理重复发送警告');
            this.closeMessageBox(element);
        };

        this.handleAccountAlert = async (element) => {
            //console.log('处理账户状态提醒');
            if (this.retryCount >= this.maxRetries) {
                //console.log('超过最大重试次数');
                return;
            }
            await this.wait(this.retryInterval);
            this.retryCount++;
        };

        this.handleLoginAlert = async (element) => {
            //console.log('处理账户登录提示');
            const refreshBtn = element.querySelector('.foot span');
            if (refreshBtn) {
                //console.log('找到刷新按钮，点击刷新');
                refreshBtn.click();
            } else {
                //console.log('未找到刷新按钮，刷新整个页面');
                window.location.reload();
            }
        };

        this.handleNetworkAlert = async (element) => {
            //console.log('处理网络问题提示');
            if (this.retryCount >= this.maxRetries) {
                //console.log('超过最大重试次数');
                return;
            }

            const refreshBtn = element.querySelector('.foot span');
            if (refreshBtn) {
                //console.log('找到刷新按钮，点击刷新');
                refreshBtn.click();
            } else {
                //console.log('未找到刷新按钮，刷新整个页面');
                window.location.reload();
            }
            
            this.retryCount++;
            await this.wait(this.retryInterval);
        };

        this.handleServiceScoreWarning = async (element) => {
            //console.log('处理服务评分警告对话框');
            const closeButton = element.querySelector('.close-button-icon');
            if (closeButton) {
                //console.log('找到关闭按钮，点击关闭');
                closeButton.click();
            }
        };

        // 然后再设置handlers对象
        this.handlers = {
            'platform-takeover': this.handlePlatformTakeover,
            'refund-request': this.handleRefundRequest,
            'repeat-warning': this.handleRepeatWarning,
            'account-alert': this.handleAccountAlert,
            'login-alert': this.handleLoginAlert,
            'network-alert': this.handleNetworkAlert,
            'service-score-warning': this.handleServiceScoreWarning
        };

        this.observer = new MutationObserver(this.handleMutations.bind(this));
    }

    shouldTransferToHuman(scene) {
        return this.transferScenes[scene] === true;
    }

    async transferToHuman(reason) {
        //console.log('转人工处理:', reason);
        // 查找转移会话按钮
        const transferBtn = document.querySelector('.transfer-chat');
        if (transferBtn) {
            transferBtn.click();
            await this.wait(500);
            
            // 选择转移原因
            const reasonItems = document.querySelectorAll('.trasnfer-remark-item');
            for (const item of reasonItems) {
                if (item.textContent.includes(reason)) {
                    const sendBtn = item.querySelector('.submit-span');
                    if (sendBtn) {
                        sendBtn.click();
                        return true;
                    }
                }
            }
        }
        return false;
    }

    startObserving(targetNode = document.body) {
        const config = {
            childList: true,
            subtree: true,
            attributes: true
        };
        this.observer.observe(targetNode, config);
        //console.log('消息框监听已启动');
    }

    stopObserving() {
        this.observer.disconnect();
        //console.log('消息框监听已停止');
    }

    async handleMutations(mutations) {
        for (const mutation of mutations) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) {
                        this.handleNewElement(node);
                    }
                });
            }
        }
    }

    async handleNewElement(element) {
        try {
            const type = this.identifyMessageBox(element);
            //console.log('发现新元素:', element, '类型:', type);
            if (this.handlers[type]) {
                await this.handlers[type](element);
                return;
            }
            await this.handleUnknownMessageBox(element);
        } catch (error) {
            console.error('处理消息框时出错:', error);
            // 默认错误处理，不再调用不存在的fallbackHandler
            this.closeMessageBox(element);
        }
    }

    identifyMessageBox(element) {
        const content = element.textContent || '';
        //console.log('正在识别消息框:', content);
        
        if (element.classList.contains('service-score-warning-dialog')) {
            return 'service-score-warning';
        }
        
        if (element.classList.contains('negative_forbidden_to_send_msg')) {
            return 'platform-takeover';
        }
        
        if (this.characteristics.login.some(word => content.includes(word))) {
            return 'login-alert';
        }
        
        if (this.characteristics.network.some(word => content.includes(word))) {
            return 'network-alert';
        }

        if (element.classList.contains('alert')) {
            const title = element.querySelector('.head')?.textContent || '';
            if (title.includes('账户')) return 'login-alert';
            if (title.includes('网络')) return 'network-alert';
        }
        
        return null;
    }

    async handleUnknownMessageBox(element) {
        const content = element.textContent || '';
        //console.log('处理未知消息框:', content);
        
        const isBlocking = this.characteristics.blocking.some(word => content.includes(word));
        const isWarning = this.characteristics.warning.some(word => content.includes(word));
        const needsConfirmation = this.characteristics.confirmation.some(word => content.includes(word));

        if (isBlocking) {
            await this.handleBlockingMessage(element);
        } else if (needsConfirmation) {
            await this.handleConfirmation(element);
        } else if (isWarning) {
            console.log('检测到警告消息:', element.textContent);
            this.closeMessageBox(element);
        }
    }

    async handleBlockingMessage(element) {
        //console.log('处理阻塞性消息');
        this.closeMessageBox(element);
    }

    async handleConfirmation(element) {
        //console.log('处理确认框');
        if (this.isSafeAction(element.textContent || '')) {
            this.clickConfirm(element);
        }
    }

    closeMessageBox(element) {
        const closeBtn = element.querySelector('[class*="close"], [class*="关闭"]');
        if (closeBtn) {
            closeBtn.click();
        }
    }

    clickConfirm(element) {
        const confirmBtn = element.querySelector('[class*="confirm"], [class*="确定"]');
        if (confirmBtn) {
            confirmBtn.click();
        }
    }

    isSafeAction(text) {
        const safeWords = ['我知道了', '确定', '继续', '关闭'];
        const dangerWords = ['删除', '清空', '退款', '取消订单'];
        
        return safeWords.some(word => text.includes(word)) &&
               !dangerWords.some(word => text.includes(word));
    }

    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 导出模块
window.MessageBoxHandler = MessageBoxHandler; 