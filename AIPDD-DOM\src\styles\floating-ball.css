.floating-ball {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translate(0, -50%);
    width: 48px;
    height: 48px;
    z-index: 9999;
    cursor: pointer;
    user-select: none;
    border-radius: 50%;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 2px solid white;
    padding: 2px;
    transition: all 0.3s ease;
}

.floating-ball .icon-wrapper {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

.floating-ball .icon-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.floating-ball.auto-reply-active {
    border-color: #1890ff;
}

.floating-ball.auto-reply-active .icon-wrapper {
    background-color: #1890ff;
}

.floating-menu {
    position: absolute;
    right: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    padding: 8px 0;
    margin-right: 12px;
    min-width: 160px;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.floating-menu.visible {
    opacity: 1;
}

.menu-header {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
}

.close-btn {
    cursor: pointer;
    font-size: 20px;
    line-height: 1;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.close-btn:hover {
    background-color: rgba(0,0,0,0.04);
}

.menu-content {
    padding: 8px 0;
}

.menu-item {
    padding: 10px 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    animation: menuItemFadeIn 0.3s ease forwards;
    opacity: 0;
    white-space: nowrap;
}

.menu-item:hover {
    background-color: rgba(0,0,0,0.04);
}

.menu-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toggle-switch {
    margin-left: auto;
    width: 36px;
    height: 20px;
    background: #ddd;
    border-radius: 10px;
    position: relative;
    transition: background-color 0.3s ease;
}

.toggle-switch::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    top: 2px;
    left: 2px;
    transition: transform 0.3s ease;
}

.auto-reply-active .toggle-switch {
    background: #1890ff;
}

.auto-reply-active .toggle-switch::after {
    transform: translateX(16px);
}

.menu-divider {
    height: 1px;
    background: #f0f0f0;
    margin: 4px 0;
}

.status-processing {
    animation: processing 1.5s infinite;
}

.status-success .icon-wrapper {
    background: #52c41a;
}

.status-warning .icon-wrapper {
    background: #faad14;
}

.status-error .icon-wrapper {
    background: #f5222d;
}

@keyframes processing {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes menuItemFadeIn {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.menu-item:nth-child(1) { animation-delay: 0.1s; }
.menu-item:nth-child(2) { animation-delay: 0.2s; }
.menu-item:nth-child(3) { animation-delay: 0.3s; }

@keyframes toggleBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.toggle-switch.active::after {
    animation: toggleBounce 0.3s ease;
}

.settings-dialog,
.about-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: dialogFadeIn 0.3s ease;
}

.settings-content,
.about-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    width: 400px;
    max-width: 90%;
    animation: dialogSlideIn 0.3s ease;
}

.settings-header,
.about-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.settings-header h3,
.about-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

.settings-body,
.about-body {
    padding: 20px;
}

.settings-footer {
    padding: 16px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.settings-item {
    margin-bottom: 16px;
}

.settings-item label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #333;
}

.settings-item input {
    width: 100%;
    padding: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
}

.settings-item input:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.close-btn {
    cursor: pointer;
    font-size: 20px;
    line-height: 1;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.close-btn:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

button {
    padding: 8px 16px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    background: white;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

button:hover {
    border-color: #1890ff;
    color: #1890ff;
}

.save-btn {
    background: #1890ff;
    border-color: #1890ff;
    color: white;
}

.save-btn:hover {
    background: #40a9ff;
    border-color: #40a9ff;
    color: white;
}

.about-body {
    text-align: center;
    padding: 24px;
}

.about-body p {
    margin: 8px 0;
    color: #666;
}

@keyframes dialogFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes dialogSlideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 设置对话框样式 */
.settings-content {
    width: 460px;
    max-height: none;
    overflow-y: visible;
}

.settings-body {
    padding: 20px;
}

.section {
    margin-bottom: 24px;
}

.section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.option-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 2px 0;
}

.option-label {
    flex: 1;
    padding-right: 16px;
}

.option-description {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
    line-height: 1.4;
}

/* 关键词列表样式 */
.keyword-group {
    margin-top: 12px;
}

.keyword-input {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.keyword-input input {
    flex: 1;
    height: 32px;
    padding: 6px 12px;
}

.keyword-input button {
    padding: 6px 16px;
    height: 32px;
}

.keyword-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.keyword-item {
    padding: 4px 10px;
    font-size: 13px;
}

/* 客服组设置样式 */
select, input[type="text"] {
    height: 32px;
    padding: 6px 12px;
}

/* 保存按钮样式优化 */
.settings-footer {
    padding: 12px 20px;
}

.save-btn, .cancel-btn {
    height: 32px;
    padding: 0 16px;
}

/* 保存成功提示 */
.save-success {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #000000e6;
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    font-size: 14px;
    opacity: 0;
    z-index: 100001;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.save-success::before {
    content: "✓";
    font-size: 16px;
    color: #52c41a;
    font-weight: bold;
}

.save-success.visible {
    opacity: 1;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 22px;
    flex-shrink: 0;
    margin-top: 2px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 22px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

input:checked + .slider {
    background-color: #1890ff;
}

input:checked + .slider:before {
    transform: translateX(22px);
}

/* 按钮和输入框样式 */
button {
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-size: 14px;
    line-height: 20px;
}

button:hover {
    background: #40a9ff;
}

.cancel-btn {
    background: white;
    border: 1px solid #d9d9d9;
    color: rgba(0, 0, 0, 0.65);
}

.cancel-btn:hover {
    color: #40a9ff;
    border-color: #40a9ff;
    background: white;
}

input[type="text"], select {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    width: 200px;
    background-color: white;
}

input[type="text"]:focus, select:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 关键词项样式 */
.keyword-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #f5f5f5;
    border-radius: 4px;
}

.remove-keyword {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 0;
    font-size: 16px;
    line-height: 1;
    border-radius: 50%;
}

.remove-keyword:hover {
    color: #f5222d;
    background: rgba(245, 34, 45, 0.1);
}

/* 设置页面布局 */
.settings-footer {
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* 保存成功提示动画 */
@keyframes tipFadeInOut {
    0% { 
        opacity: 0;
        transform: translate(-50%, calc(-50% + 20px));
    }
    20% {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
    80% {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, calc(-50% - 20px));
    }
}

.save-success.visible {
    animation: tipFadeInOut 1.5s cubic-bezier(0.23, 1, 0.32, 1) forwards;
}

#aiSettingsSaveSuccess {
    display: none;
    text-align: center;
    color: #059669;
    margin-top: 12px;
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.3s ease;
    padding: 8px;
    border-radius: 4px;
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
} 