import { DEFAULT_CONFIG, STORAGE_KEY } from '../config/default.config.js';

export class DifyAPI {
    constructor() {
        this.config = null;
        this.initialized = false;
    }

    async initialize(config) {
        try {
            if (!config) {
                config = await this.loadConfig();
            }

            if (!config.apiKey || !config.apiUrl) {
                console.error('Invalid Dify API configuration');
                this.config = DEFAULT_CONFIG.dify;
                return false;
            }

            // 验证API连接
            const response = await fetch(`${config.apiUrl}/chat-messages`, {
                method: 'HEAD',
                headers: {
                    'Authorization': `Bearer ${config.apiKey}`
                }
            });
            
            if (!response.ok) {
                console.error(`API validation failed: ${response.status}`);
                this.config = DEFAULT_CONFIG.dify;
                return false;
            }

            this.config = config;
            this.initialized = true;
            return true;
        } catch (error) {
            console.error('Initialization error:', error);
            this.initialized = false;
            this.config = DEFAULT_CONFIG.dify;
            return false;
        }
    }

    async loadConfig() {
        try {
            const result = await chrome.storage.local.get(STORAGE_KEY);
            return result[STORAGE_KEY] || DEFAULT_CONFIG.dify;
        } catch (error) {
            console.error('Error loading Dify config:', error);
            return DEFAULT_CONFIG.dify;
        }
    }

    getConfig() {
        return this.config || DEFAULT_CONFIG.dify;
    }

    // 为了兼容性，添加chat方法作为sendMessage的别名
    async chat(message, stream = false, callbacks = {}) {
        return this.sendMessage(message, stream, callbacks);
    }

    async sendMessage(message, stream = false, callbacks = {}) {
        if (!this.initialized || !this.config) {
            throw new Error('Dify API not initialized');
        }

        try {
            const response = await fetch(`${this.config.apiUrl}/chat-messages`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.config.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    inputs: {},
                    query: message,
                    user: DEFAULT_CONFIG.dify.defaultUser,
                    response_mode: stream ? 'streaming' : 'blocking'
                })
            });

            if (!response.ok) {
                throw new Error(`Dify API error: ${response.status}`);
            }

            if (stream) {
                await this.handleStream(response.body, callbacks);
                return response;
            }

            return await response.json();
        } catch (error) {
            console.error('Error sending message:', error);
            throw error;
        }
    }

    async handleStream(stream, callbacks = {}) {
        const reader = stream.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        try {
            while (true) {
                const { value, done } = await reader.read();
                if (done) break;

                const text = decoder.decode(value);
                buffer += text;

                // 尝试解析每个完整的 JSON 对象
                const lines = buffer.split('\n');
                buffer = lines.pop(); // 保留最后一个可能不完整的行

                for (const line of lines) {
                    if (line.trim()) {
                        try {
                            const data = JSON.parse(line);
                            if (callbacks.onMessage) {
                                callbacks.onMessage(data.answer || data.text || data);
                            }
                        } catch (error) {
                            if (callbacks.onError) {
                                callbacks.onError(error);
                            }
                        }
                    }
                }
            }

            // 处理最后的缓冲区
            if (buffer.trim()) {
                try {
                    const data = JSON.parse(buffer);
                    if (callbacks.onMessage) {
                        callbacks.onMessage(data.answer || data.text || data);
                    }
                } catch (error) {
                    console.error('Error parsing final buffer:', error);
                    if (callbacks.onError) {
                        callbacks.onError(error);
                    }
                }
            }
        } catch (error) {
            console.error('Error reading stream:', error);
            if (callbacks.onError) {
                callbacks.onError(error);
            }
            throw error;
        } finally {
            try {
                reader.releaseLock();
            } catch (error) {
                console.error('Error releasing lock:', error);
            }
        }
    }

    // 辅助方法
    async getAppInfo(user) {
        if (!this.initialized || !this.config) {
            throw new Error('Dify API not initialized');
        }

        const response = await fetch(`${this.config.apiUrl}/info?user=${user}`, {
            headers: {
                'Authorization': `Bearer ${this.config.apiKey}`
            }
        });

        if (!response.ok) {
            throw new Error(`Failed to get app info: ${response.status}`);
        }

        return response.json();
    }

    async getParameters(user) {
        if (!this.initialized || !this.config) {
            throw new Error('Dify API not initialized');
        }

        const response = await fetch(`${this.config.apiUrl}/parameters?user=${user}`, {
            headers: {
                'Authorization': `Bearer ${this.config.apiKey}`
            }
        });

        if (!response.ok) {
            throw new Error(`Failed to get parameters: ${response.status}`);
        }

        return response.json();
    }

    async deleteConversation(conversationId, user) {
        if (!this.initialized || !this.config) {
            throw new Error('Dify API not initialized');
        }

        const response = await fetch(`${this.config.apiUrl}/conversations/${conversationId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${this.config.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ user })
        });

        if (!response.ok) {
            throw new Error(`Failed to delete conversation: ${response.status}`);
        }

        return response.json();
    }
}

export const difyAPI = new DifyAPI();
