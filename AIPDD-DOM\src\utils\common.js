/**
 * v2通用预留 模拟点击指定的 DOM 元素
 * 模拟点击指定的 DOM 元素 (同步版本)
 * @param {HTMLElement} element - 要点击的 DOM 元素
 * @returns {boolean} - 如果点击成功返回 true，否则返回 false
 */
export function simulateClickV2(element) {
    if (!element) {
        console.error('异常: 要点击的元素不存在');
        return false;
    }

    try {
        element.click();
        return true;
    } catch (error) {
        console.error('Error simulating click:', error);
        return false;
    }
}

/**
 * v2通用预留 模拟点击指定的 DOM 元素
 * @param {HTMLElement} element - 要点击的 DOM 元素
 * @param {boolean} [useRealClick=false] - 是否使用真实点击事件，默认为 false
 * @returns {boolean} - 如果点击成功返回 true，否则返回 false
 */
export async function asyncSimulateClickV2(element, useRealClick = false) {
    console.debug('[dify]要点击的按钮',element);
    if (!element) {
        
        console.error('异常: 要点击的元素不存在');
        return false;
    }

    try {
        if (useRealClick) {
            // 使用真实点击事件
            // 确保元素在视图中
            if(typeof element.scrollIntoView === 'function'){
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
            await sleepV2(500);

            // 模拟真实点击
            let rect = element.getBoundingClientRect();
            let clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window,
                clientX: rect.left + rect.width / 2,
                clientY: rect.top + rect.height / 2
            });
            element.dispatchEvent(clickEvent);
        } else {
            // 使用 element.click() 方法
            element.click();
        }
        return true;
    } catch (error) {
        console.error('Error simulating click:', error);
        return false;
    }
}

// 辅助函数，用于等待一段时间
export function sleepV2(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 将远程 URL 图片转换为 Base64 编码
export async function convertImageUrlToBase64(imageUrl) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.onload = function () {
            const reader = new FileReader();
            reader.onloadend = function () {
                resolve(reader.result);
            };
            reader.onerror = reject;
            reader.readAsDataURL(xhr.response);
        };
        xhr.onerror = reject;
        xhr.open('GET', imageUrl);
        xhr.responseType = 'blob';
        xhr.send();
    });
}

/**
 * 模拟粘贴图片资源到指定的 DOM 元素
 * @param {HTMLElement|string} elementOrSelector - 要粘贴的 DOM 元素或 CSS 选择器
 * @param {string|File} imageSource - 图片资源的 URL 或 File 对象
 * @param {string} fileName - 文件名
 * @returns {boolean} - 如果粘贴成功返回 true，否则返回 false
 */
export async function simulatePasteImage(elementOrSelector, imageSource, fileName) {
    let element;
    if (typeof elementOrSelector === 'string') {
        element = document.querySelector(elementOrSelector);
    } else {
        element = elementOrSelector;
    }
    if (!element) {
        console.error('[dify]异常: 要粘贴的元素不存在');
        return false;
    }

    try {
        console.debug('[dify]准备获取图片');
        let blob;
        if (typeof imageSource === 'string') {
            // 如果 imageSource 是 URL
            const response = await fetch(imageSource);
            if (!response.ok) {
                console.error('[dify]获取图片资源失败:', response.status, response.statusText);
                return false;
            }
            blob = await response.blob();
        } else if (imageSource instanceof File) {
            // 如果 imageSource 是 File 对象
            blob = imageSource;
        } else {
            console.error('[dify]无效的图片资源类型:', imageSource);
            return false;
        }
        console.debug('[dify]准备创建file对象');
        // 创建 File 对象
        const file = new File([blob], fileName, { type: blob.type });
        console.debug('[dify]准备创建datatransfer');
        // 创建 DataTransfer 对象
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        console.debug('[dify]准备创建ClipboardEvent');
        // 创建 ClipboardEvent 对象
        const pasteEvent = new ClipboardEvent('paste', {
            clipboardData: dataTransfer,
            bubbles: true,
            cancelable: true
        });
        console.debug('[dify]触发 paste 事件');
        // 触发 paste 事件
        element.dispatchEvent(pasteEvent);
        // 等待 div[previewimgurl] 元素加载
       // await waitForElementOrTimeout('div[previewimgurl] .btn-ok', 5000);
        console.debug('[dify]粘贴完成返回');
        return true;
    } catch (error) {
        console.error('[dify]Error simulating paste:', error);
        return false;
    }
}


// 使用 MutationObserver 等待元素出现，或超时的函数
export function waitForElementOrTimeout(selector, timeout) {
    return Promise.race([
        waitForElement(selector),
        new Promise(resolve => setTimeout(() => resolve(false), timeout))
    ]);
}

// 使用 MutationObserver 等待元素出现的函数 (同方案二)
function waitForElement(selector) {
    return new Promise(resolve => {
        const observer = new MutationObserver((mutationsList, observer) => {
            const element = document.querySelector(selector);
            if (element) {
                observer.disconnect();
                resolve(true);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    });
}

/**
 * 根据关键词分割字符串，并提取 URL 数组
 * @param {string} text - 要分割的字符串
 * @param {string} keyword - 分割关键词
 * @param {boolean} is_url - 是否提取 URL 数组
 * @returns {{text: string, urls: string[]}} - 分割后的文本和 URL 数组 
 */
export function splitTextAndExtractUrls(text, keyword, is_url = true) {
    if (!text || !keyword) {
        return { text: text, urls: [] };
    }

    const parts = text.split(keyword);
    if (parts.length < 2) {
        return { text: text, urls: [] };
    }

    const textBeforeKeyword = parts[0].trim();
    const textAfterKeyword = parts[1].trim();
    
    if(is_url) {
        const urlRegex = /(https?:\/\/[^\s]+\.(?:jpg|jpeg|gif|bmp|webp!png))/g;
        const urls = Array.from(textAfterKeyword.matchAll(urlRegex), match => match[0]);

        const textRegex = /(?:https?:\/\/[^\s]+\.(?:jpg|jpeg|gif|bmp|webp!png))|([\s\S]+?(?=https?:\/\/|$))/g;
        const texts = Array.from(textAfterKeyword.matchAll(textRegex))
            .filter(match => match[1]) // 只保留非 URL 的匹配组
            .map(match => match[1].trim()) // 获取第一个捕获组的内容并去除首尾空格
            .join(''); // 将所有文本片段连接起来

        return { text: textBeforeKeyword + texts, urls: urls };
    }else {
        // 只按视频扩展名分割
        const extensions = ['.mp4', '.avi', '.mov', '.wmv'];
        let result = [];
        let temp = '';
        for (let i = 0; i < textAfterKeyword.length; i++) {
            temp += textAfterKeyword[i];
            for (const ext of extensions) {
                if (temp.endsWith(ext)) {
                    result.push(temp.trim());
                    temp = '';
                    break;
                }
            }
        }
        if(temp.trim()) result.push(temp.trim());
        return { text: textBeforeKeyword, urls: result.filter(x => extensions.some(ext => x.endsWith(ext))) };
    }
}

//单字符串分割
export function toolSplitText(c, s) {
    const parts = c.split(s);
    if (parts.length > 0) {
        return { before: parts[0]?.trim(), after: parts[1]?.trim() };
    }

    return [  ];
}


function splitStringByVideoExtensions(text) {
    const extensions = ['.avi', '.mp4', '.mov', '.wmv'];
    let separator = null;
  
    // 查找字符串中是否存在任何一个视频扩展名
    for (const ext of extensions) {
      if (text.includes(ext)) {
        separator = ext;
        break;
      }
    }
  
    if (separator) {
      // 如果找到了分隔符，则进行分割
      return text.split(separator);
    } else {
      // 如果没有找到分隔符，则返回包含原始字符串的数组
      return [text];
    }
  }

/**
 * 格式化订单信息为可读文本
 * @param {Object} orderInfo - 订单信息对象
 * @returns {string} - 格式化后的订单信息文本
 */
export function formatOrderInfo(orderInfo) {
    if (!orderInfo) return '';

    let formattedText = '';

    // 按照指定格式组织订单信息
    // 订单数：*
    if (orderInfo.orderCount) {
        formattedText += `订单数：${orderInfo.orderCount}\n`;
    }

    // 订单状态：**
    if (orderInfo.orderStatus) {
        formattedText += `订单状态：${orderInfo.orderStatus}\n`;
    }

    // 商品名：***
    if (orderInfo.goodsName) {
        formattedText += `商品名：${orderInfo.goodsName}\n`;
    }

    // 实付金额：**
    if (orderInfo.amountValue) {
        formattedText += `实付金额：${orderInfo.amountValue}\n`;
    } else if (orderInfo.goodsPrice) {
        // 如果没有实付金额，使用商品价格作为备选
        formattedText += `实付金额：${orderInfo.goodsPrice}\n`;
    }

    // 数量：**
    if (orderInfo.goodsNum) {
        formattedText += `数量：${orderInfo.goodsNum}\n`;
    }

    // 备注：**
    if (orderInfo.remarkNote) {
        formattedText += `备注：${orderInfo.remarkNote}\n`;
    }

    // 下单时间：**
    if (orderInfo.orderTime) {
        formattedText += `下单时间：${orderInfo.orderTime}\n`;
    }

    return formattedText.trim(); // 去除末尾的换行符
}

/**
 * 从固定格式的字符串中提取商品名称/ID、金额、数量和定制天数（支持换行符）
 * @param {string} input - 输入的字符串
 * @returns {Object} 包含提取内容的对象
 */
export function extractOrderInfo(input) {
    const result = {
        prefixText: null,  // 前置文案
        productId: null,   // 商品ID（全数字）
        productName: null, // 商品名称（非全数字）
        amount: null,      // 金额
        quantity: null,    // 数量
        customDays: null,  // 定制天数
        suffixText: null,  // 后置文案
    };

    // 提取前置文案（从字符串开头到"下单："之前）
    const prefixMatch = input.match(/^([\s\S]*?)下单：/);
    if (prefixMatch) {
        result.prefixText = prefixMatch[1].trim();
        result.prefixText = result.prefixText.length > 24 ? result.prefixText.substring(0, 24) : result.prefixText;
    }

    // 提取商品名称或ID（从"下单："到全角逗号或字符串末尾）
    const productMatch = input.match(/下单：([^，\n]+)/);
    if (productMatch) {
        const product = productMatch[1].trim();
        // 判断是否为全数字
        if (/^\d+$/.test(product)) {
            result.productId = product;
        } else {
            result.productName = product;
        }
    }

    // 提取定制天数（如果有）
    const customDaysMatch = input.match(/定制天数：(\d+)天/);
    if (customDaysMatch) {
        result.customDays = customDaysMatch[1].trim();
    }

    // 提取金额（如果有）
    const amountMatch = input.match(/金额：([^元]+)元/);
    if (amountMatch) {
        const amount = parseFloat(amountMatch[1].trim());
        result.amount = isNaN(amount) ? null : amount;
    }

    // 提取数量
    const quantityMatch = input.match(/拍下数量：([^个]+)个/);
    if (quantityMatch) {
        const quantity = parseInt(quantityMatch[1].trim());
        result.quantity = isNaN(quantity) ? null : quantity;
    }

    // 提取后置文案（从"数量：x个"之后到字符串末尾，支持句号或"个句号"）
    const suffixMatch = input.match(/拍下数量：[^个]+个[。.]?([\s\S]*)/);
    if (suffixMatch && suffixMatch[1]) {
        result.suffixText = suffixMatch[1].trim();
    }

    return result;
}