import { asyncSimulateClickV2,simulatePasteImage, waitForElementOrTimeout, splitTextAndExtractUrls, sleepV2, toolSplitText,extractOrderInfo } from './common.js';

/**
 * v2通用预留 Dify 回复处理结果类型
 * @typedef {Object} DifyResponseResult
 * @property {'success'} status - 成功状态
 * @property {string} [imagePath] - 图片路径，仅在成功时存在
 * @property {'not_triggered'} status - 未触发状态
 * @property {'error'} status - 错误状态
 * @property {string} message - 错误消息，仅在错误时存在
 */

/**
 * Dify 回复处理器接口
 * @interface DifyResponseHandler
 * @method handle
 * @param {string} difyResponse - Dify 的回复
 * @returns {DifyResponseResult} - 处理结果
 */

/**
 * 默认的 Dify 回复处理器
 * @implements {DifyResponseHandler}
 */
export class DefaultDifyResponseHandler {
    async handle(difyResponse) {
        if (!difyResponse) {
            console.debug('Dify 回复为空，不进行处理');
            return { status: 'not_triggered', message: '未触发发送图片' };
        }

        console.debug('处理 Dify 回复:', difyResponse);

        // 检查“发图片：”和“发视频：”的顺序
        let trimmedResponse = difyResponse.trim();
        const img_prefix = '发图片：';
        const video_prefix = '发视频：';
        const money_prefix = '打款：';
        const order_prefix = '下单：';

        // 同时存在时，按先后顺序处理
        let imgIdx = trimmedResponse.indexOf(img_prefix);
        let videoIdx = trimmedResponse.indexOf(video_prefix);

        if (imgIdx !== -1 && videoIdx !== -1) {
            // 两者都存在，按先后顺序处理
            if (imgIdx < videoIdx) {
                // 先发图片，再发视频
                let beforeImg = trimmedResponse.substring(0, imgIdx).trim();
                let imgPart = trimmedResponse.substring(imgIdx, videoIdx);
                let videoPart = trimmedResponse.substring(videoIdx);

                // 处理发图片
                let { text: imgText, urls: imgUrls } = splitTextAndExtractUrls(imgPart, img_prefix);
                if (beforeImg) await sendMessage(beforeImg);
                if (imgText) await sendMessage(imgText);
                if (imgUrls.length > 0) await sendImgs(imgUrls);

                // 处理发视频
                let { text: videoText, urls: videoKeys } = splitTextAndExtractUrls(videoPart, video_prefix, false);
                if (videoText) await sendMessage(videoText);
                if (videoKeys.length > 0) await sendVideos(videoKeys);

                return { status: 'success', message: '' };
            } else {
                // 先发视频，再发图片
                let beforeVideo = trimmedResponse.substring(0, videoIdx).trim();
                let videoPart = trimmedResponse.substring(videoIdx, imgIdx);
                let imgPart = trimmedResponse.substring(imgIdx);

                // 处理发视频
                let { text: videoText, urls: videoKeys } = splitTextAndExtractUrls(videoPart, video_prefix, false);
                if (beforeVideo) await sendMessage(beforeVideo);
                if (videoText) await sendMessage(videoText);
                if (videoKeys.length > 0) await sendVideos(videoKeys);

                // 处理发图片
                let { text: imgText, urls: imgUrls } = splitTextAndExtractUrls(imgPart, img_prefix);
                if (imgText) await sendMessage(imgText);
                if (imgUrls.length > 0) await sendImgs(imgUrls);

                return { status: 'success', message: '' };
            }
        } else if (imgIdx !== -1) {
            // 只有发图片
            let { text, urls } = splitTextAndExtractUrls(trimmedResponse, img_prefix);
            if (text) await sendMessage(text);
            if (urls.length > 0) await sendImgs(urls);
            return { status: 'success', message: '' };
        } else if (videoIdx !== -1) {
            // 只有发视频
            let { text, urls: searchKeys } = splitTextAndExtractUrls(trimmedResponse, video_prefix, false);
            if (text) await sendMessage(text);
            if (searchKeys.length > 0) await sendVideos(searchKeys);
            return { status: 'success', message: '' };
        } else if(trimmedResponse.includes(money_prefix)) {
            //小额打款
            let {before, after} = toolSplitText(trimmedResponse, money_prefix);
            if(after) {
                const money_text = after.match(/(\d+)元/);
                if(money_text) {
                    const amount = money_text[1];
                    let sendResult = await sendMoney(amount);
                    if(sendResult === true) {
                        return { status: 'success', message: before };
                    }else {
                        return { status: 'success', message: '抱歉，我暂时没权限。给您转到专人处理可以吗？' };
                    }
                }
            }
            
            return { status: 'success', message: '抱歉，我暂时没权限,给您转到专人处理可以吗？' };
        }else if( trimmedResponse.includes(order_prefix) ) {
            //邀请下单
            let rtn_text = '';
            //解析格式
            let orderInfo = extractOrderInfo(trimmedResponse);
            if(orderInfo.quantity) {
                //商品ID
                let sendResult = await createOrder(orderInfo);
                if(sendResult) {
                    if(orderInfo.suffixText) {
                        rtn_text = orderInfo.suffixText;
                    }else {
                        rtn_text = '您点击链接下单即可';
                    }
                    
                }

                return { status: 'success', message: rtn_text };
            }


            return { status: 'not_triggered', message: '未触发' };
        }else {
            return { status: 'not_triggered', message: '未触发' };
        }

        
    }
}

async function sendImgs(urls) {
    for(let url of urls) {
        let result = await sendImg(url);
        if(!result.success) {
            return false;
        }
        await sleepV2(600);
        debug('发图成功2');
    }
    return true;
}

async function sendVideos(searchKeys) {
    for(let key of searchKeys) {
        let result = await sendVideo(key);
        if(!result) {
            return false;
        }
        await sleepV2(600);
    }
    return true;
}

async function sendImg(url) {

    //模拟点击发送图片图标按钮
    let textarea_reply = document.querySelector("textarea#replyTextarea");
    await simulatePasteImage(textarea_reply, url, "tmpsendimg")
    await sleepV2(300); // 添加延时，等待图片预览出现

    //判断是否要辅助发送
    let aiSettings = await chrome.storage.local.get('aiSettings');
    if(aiSettings.aiSettings) {
        //辅助发送
        let assistReplyEnabled = aiSettings.aiSettings.assistReplyEnabled;
        console.debug('[debug] assistReplyEnabled:', assistReplyEnabled);
        if(assistReplyEnabled) {
            //辅助发送图片
            return { success: true, completed: false }; // 返回成功但未完成状态
        }
    }
    
    if(await waitForElementOrTimeout('div[previewimgurl]', 5000)){
        let sendImgBtn = document.querySelector('div[previewimgurl] .btn-ok');
        if(sendImgBtn){
            await asyncSimulateClickV2(sendImgBtn, true);
            await sleepV2(500);
            
            debug('发图成功1');
            return { success: true, completed: true };
        }else {
            console.error('[dify] 未找到发送按钮');
            return { success: false, completed: false };
        }
    }else{
        console.error('[dify] 发送图片异常');
        return { success: false, completed: false };
    }
}

async function sendVideo(searchKey) {
    console.debug('[dify]进入发送视频');
    //搜索
    // let tips = document.querySelector('.send-video-tips');
    // if(tips) {  
    //     tips.style.display = 'block';
    // }

    await asyncSimulateClickV2(document.querySelector('.chat-icon-camera.el-popover__reference'),true);
    await sleepV2(500);

    let search_input = document.querySelector('.send-video-tips .send-video-tips-search .el-input input');
    console.debug('[dify]找到输入框',search_input);
    
    search_input.value = searchKey;
    search_input.dispatchEvent(new Event('input', { bubbles: true }));
    console.debug('[dify]触发 paste 事件');

    await sleepV2(1000);

    //发送
    let video_no_result = document.querySelector('.send-video-tips .video-no-result');
    if(video_no_result) {
        //未搜索到视频
        return false;
    }
    console.debug('[dify]搜索有结果');
    let send_video_btns = document.querySelectorAll('.send-video-tips .video-list-box .el-button.send-video-btn');
    if(send_video_btns[0]) {
        console.debug('[dify]准备点击发送',send_video_btns[0]);
        //发送视频
        await asyncSimulateClickV2(send_video_btns[0],true);
        return true;
    }


    return false;
    
}


async function sendMoney(amount) {
    console.debug('[debug]进入打款',amount);
    let order_item = await findUserOrderCard();

    if(order_item) {
        //点击打款按钮
        return await actionSendMoney(amount, order_item);
    }
    return false;
}


/**
 * 创建 Dify 回复处理器
 * @param {string} type - 处理器类型
 * @returns {DifyResponseHandler} - Dify 回复处理器实例
 */
export function createDifyResponseHandler(type = 'default') {
    switch (type) {
        case 'default':
        default:
            return new DefaultDifyResponseHandler();
    }
}

//查找按钮
async function findUserOrderCard() {
    let bar_items = document.querySelectorAll('.right-panel-container .bar-box.four-tab .bar-item');

    for (let i = 0; i < bar_items.length; i++) { // 修改循环方式
        let bar_item_tab = bar_items[i]; // 定义 bar_item_tab
        if(bar_item_tab.textContent == '最新订单') {
            //模拟点击最新订单
            bar_item_tab.click();

            if (await waitForElementOrTimeout('.order-panel-second-bar', 5000)) {
                // 执行你的回调函数
                let order_panel_second_bar = document.querySelectorAll('.order-panel-second-bar');
                if(order_panel_second_bar[0] && order_panel_second_bar[0].textContent == '个人订单') {
                    order_panel_second_bar[0].click();

                    if (await waitForElementOrTimeout('.order-item-list .order-item', 5000)) {
                        //个人订单面板
                        let order_items = document.querySelectorAll('.order-item-list .order-item');
                        if(order_items.length > 0) {
                            //获取订单状态
                            let first_order_item = order_items[0];
                            return first_order_item;
                        }
                        
                        return null;
                    }
                }
            }
            
        }
    }

    return null;
}

//添加备注
async function actionSendMoney(amount, order_item) {
    debug('[debug] 传入得amount:', amount);
    //获取备注输入框
    let btn_items = order_item.querySelectorAll('.order-btn .order-btn-item');

    if(btn_items.length > 0 ) {
        for(let i = 0; i < btn_items.length; i++) {
            let btn_item = btn_items[i];
            
            if(btn_item.textContent.includes('小额打款') ) {
                debug('[debug] 按钮item:', btn_item);
                //btn_item.click();
                if(typeof btn_item.scrollIntoView === 'function'){
                    btn_item.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                await sleepV2(500);
                let rect = btn_item.getBoundingClientRect();
                let clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window,
                    clientX: rect.left + rect.width / 2,
                    clientY: rect.top + rect.height / 2
                });
                btn_item.dispatchEvent(clickEvent);
                //匹配小额打款弹窗
                if (await waitForElementOrTimeout('.little-pay-detail', 5000)) {
                    

                    let little_pay_detail = document.querySelectorAll('.little-pay-detail');
                    debug('[debug] little-pay-detail:',little_pay_detail);
                    let dialog_wrapper = null;

                    dialog_wrapper = getDialogWrapper(2);
                    if(dialog_wrapper) {
                        //找到弹窗，获取金额输入框
                        let send_money_input = dialog_wrapper.querySelector('form .el-form-item input.el-input__inner');
                        debug('[debug] send_money_input:',send_money_input);
                        if(send_money_input) {
                            send_money_input.focus(); // 获取焦点
                        
                            // 获取原生 textarea 元素的 value 属性的 setter 方法
                            let nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;

                            // 调用 setter 方法，手动更新 value 属性
                            nativeInputValueSetter.call(send_money_input, amount);

                            // 派发 input 事件
                            let inputEvent = new Event('input', {
                                bubbles: true,
                                cancelable: true
                            });
                            send_money_input.dispatchEvent(inputEvent);

                            // 派发 change 事件
                            let changeEvent = new Event('change', {
                                bubbles: true,
                                cancelable: true
                            });
                            send_money_input.dispatchEvent(changeEvent);
                            await sleepV2(500);
                            debug('[debug] 输入完毕:',1);
                            //保存
                            let send_money_btn = dialog_wrapper.querySelector('.dialog-footer button.el-button--primary');
                            debug('[debug] send_money_btn:',send_money_btn);
                            if(send_money_btn && send_money_btn.textContent.includes('确认')) {
                                send_money_btn.click();
                                await sleepV2(2000);
                                debug('[debug] 点击确认:',1);
                                //查是否成功
                                let err_dialog = getDialogWrapper(1);
                                if(err_dialog) {
                                    debug('[debug] 无权限:',1);
                                    return 'no_permission';
                                }
                                let confirm_dialog = getDialogWrapper(3);
                                debug('[debug] confirm_dialog:',confirm_dialog);
                                if(confirm_dialog) {
                                    debug('[debug] 确认打款:',1);
                                    let send_money_confirm_btn = confirm_dialog.querySelector('.el-message-box__btns button.el-button--primary');
                                    if(send_money_confirm_btn) {
                                        debug('[debug] 点击确认打款:',send_money_confirm_btn);
                                        send_money_confirm_btn.click();
                                        await sleepV2(1000);
                                        return true;
                                    }
                                }
                            }
                        }

                    }
                    
                }
            }
        }
    }

    return false;
}


function getDialogWrapper(flag = 1) {
    let dialogs;
    if(flag === 3) {
        dialogs = document.querySelectorAll('.el-message-box__wrapper');
    }else {
        dialogs = document.querySelectorAll('.el-dialog__wrapper');
    }
    
    for (const dialog of dialogs) {
        if(flag === 1) {
            // 检查文案是否包含"没有店铺管理员权限"
            if (dialog.textContent.includes('没有店铺管理员权限')) {
                // 检查 el-dialog__wrapper 是否可见
                const style = window.getComputedStyle(dialog);
                if (style.display !== 'none' && style.visibility !== 'hidden') {
                    //没有权限
                    return dialog;
                }
            }
        }else if(flag === 2) {
            // 检查小额打款
            if (dialog.textContent.includes('打款金额') && dialog.textContent.includes('给消费者留言') && dialog.textContent.includes('已选订单') && dialog.textContent.includes('打款类型')) {
                // 检查 el-dialog__wrapper 是否可见
                const style = window.getComputedStyle(dialog);
                if (style.display !== 'none' && style.visibility !== 'hidden') {
                    //没有权限
                    return dialog;
                }
            }
        }else if(flag === 3) {
            // 最后确认
            if (dialog.textContent.includes('确认打款') && dialog.textContent.includes('直接汇入买家账户') ) {
                // 检查 el-dialog__wrapper 是否可见
                const style = window.getComputedStyle(dialog);
                if (style.display !== 'none' && style.visibility !== 'hidden') {
                    
                    return dialog;
                }
            }
        }
    }
    return ;
}

async function createOrder(orderInfo) {

    debug('[邀请订单] 选择推荐商品',orderInfo);


    let bar_items = document.querySelectorAll('.header .bar-box.four-tab li');
    for(let i = 0; i < bar_items.length; i++) {
        let bar_item = bar_items[i];
        if(bar_item.textContent.includes('商品推荐')) {
            debug('[邀请订单] 找到tab',bar_item);
            // await asyncSimulateClickV2(bar_item, true);
            bar_item.click();
        }
    }
    
    if (await waitForElementOrTimeout('.goods-recommend-options .search-container', 3000)) {
        let product_area = document.querySelector('.goods-recommend-options .search-container');
        product_area.click();
        debug('[邀请订单] 点击下拉框');
   
   
        let product_items = document.querySelectorAll('.el-select-dropdown__item');
    
        let input_item ;

        if(orderInfo.productId) {
            //商品ID
            for(let i = 0; i < product_items.length; i++) {
                let product_item = product_items[i];
                if(product_item.textContent.includes('商品ID')) {
                    product_item.click();
                }
            }
            debug('[邀请订单] 点击商品ID');
            
            // input_item = product_area.querySelector('input[placeholder="请输入商品ID"].el-input__inner')
            
            //input_item.value = orderInfo.productId;
        }else if(orderInfo.productName) {
            //商品名称
            for(let i = 0; i < product_items.length; i++) {
                let product_item = product_items[i];
                if(product_item.textContent.includes('商品名称')) {
                    product_item.click();
                }
            }
            debug('[邀请订单] 点击商品名称');

            // input_item = document.querySelector('input[placeholder="请输入商品名称"].el-input__inner')
            
            //input_item.value = orderInfo.productName;
        }
   

        if (await waitForElementOrTimeout('.goods-recommend-options .search-container input[placeholder*="请输入商品"].el-input__inner', 2000)) {
            
            let search_text;
            if(orderInfo.productId) {
                input_item = product_area.querySelector('input[placeholder="请输入商品ID"].el-input__inner')
                search_text = orderInfo.productId;
                debug('[邀请订单] 找到input',input_item);
            }else if (orderInfo.productName) {
                input_item = product_area.querySelector('input[placeholder="请输入商品名称"].el-input__inner')
                search_text = orderInfo.productName;
                debug('[邀请订单] 找到input',input_item);
            }
            parseInputText(input_item, '')
            await sleepV2(100)
            parseInputText(input_item, search_text)
            await sleepV2(500)


            debug('[邀请订单] 开始选商品');
            //选商
            if (await waitForElementOrTimeout('#goodsListContainer .recommend-list .goodsRecommendItem', 5000)) {
                let goods_item = document.querySelector('#goodsListContainer .recommend-list .goodsRecommendItem');

                debug('[邀请订单] 找到goods_item',goods_item);

                let goods_btn = goods_item.querySelector('span.goods-btns-item.goods-btns-blue.el-popover__reference')
                debug('[邀请订单] 找到goods_btn',goods_btn);
                goods_btn.click()



                if (await waitForElementOrTimeout('div.el-dialog__wrapper div[aria-label="邀请消费者下单"].el-dialog input[role="spinbutton"].el-input__inner', 5000)) {
                    let order_item = document.querySelector('div.el-dialog__wrapper div[aria-label="邀请消费者下单"].el-dialog')

                    //定制处理
                    if(orderInfo.customDays) {
                        let diy_radio = order_item.querySelectorAll('.goods-type-radio .el-radio-group .el-radio');

                        for(let i = 0; i < diy_radio.length; i++) {
                            let diy_radio_item = diy_radio[i];
                            if(diy_radio_item.textContent.includes('定制商品')) {
                                diy_radio_item.click();
                                await sleepV2(300);
                                let dayElement = order_item.querySelector('.shipping-time input.el-input__inner');
                                debug('[邀请订单] 找到dayElement',dayElement);
                                parseInputText(dayElement, orderInfo.customDays)
                                debug('[邀请订单] 设置天数',orderInfo.customDays);
                            }
                        }
                    }


                    //设备注（前置文案）
                    if(orderInfo.prefixText) {
                        let remark_textarea = order_item.querySelector('.remark.show-icon textarea.el-textarea__inner')
                        debug('[邀请订单] 找到remark_textarea',remark_textarea);
                        parseInputText(remark_textarea, orderInfo.prefixText)
                        debug('[邀请订单] 设置前置文案',orderInfo.prefixText);
                    }

                    //数量处理
                    if(orderInfo.quantity) {
                        let order_num_input = order_item.querySelector('input[role="spinbutton"].el-input__inner')
                        debug('[邀请订单] 找到order_num_input',order_num_input);

                        parseInputText(order_num_input, orderInfo.quantity)
                        debug('[邀请订单] 设置数量',orderInfo.quantity);
                    }
                    let left_order_area = order_item.querySelector('.shopping-card.shopping-cart');
                    //金额处理
                    if(orderInfo.amount) {
                        if (await waitForElementOrTimeout('div.el-dialog__wrapper div[aria-label="邀请消费者下单"].el-dialog input[placeholder="请输入优惠券金额"]', 5000)) {
                            
                            let total_price;
                            
                            //取单价
                            let product_price_txt = left_order_area.querySelector('.goods-price');
                            if (!product_price_txt) {
                                console.error('[邀请订单]金额字符串为空');
                            } else {
                                // 去除 "￥" 前缀并转换为数字
                                let product_price = parseFloat(product_price_txt.textContent.replace(/[^\d.-]/g, ''));
                                
                                if (isNaN(product_price)) {
                                    console.error('无效的金额格式:', product_price_txt.textContent);
                                } else {
                                    // 检查 orderInfo.quantity 和 orderInfo.amount 是否为有效数字
                                    if (typeof orderInfo.quantity !== 'number' || isNaN(orderInfo.quantity) || orderInfo.quantity <= 0) {
                                        console.error('无效的数量:', orderInfo.quantity);
                                    } else if (typeof orderInfo.amount !== 'number' || isNaN(orderInfo.amount)) {
                                        console.error('无效的金额:', orderInfo.amount);
                                    } else {
                                        debug('[邀请订单] 单价金额', product_price)
                                        debug('[邀请订单] 数量', orderInfo.quantity)
                                        debug('[邀请订单] 减去金额', orderInfo.amount)
                                        total_price = parseInt( (product_price * orderInfo.quantity) - orderInfo.amount );
                                        // 检查 total_price 是否为负数
                                        if (total_price < 0) {
                                            console.warn('警告: 计算结果为负数，可能存在逻辑错误', total_price);
                                        }
                                    }
                                }
                            }
                        
                            
                            
                            let amount_input = left_order_area.querySelector('input[placeholder="请输入优惠券金额"]');
                            debug('[邀请订单] 找到amount_input',amount_input);
                            parseInputText(amount_input, total_price)
                            // amount_input.value = orderInfo.amount;
                            // const changeEvent = new Event('change', { bubbles: true });
                            // amount_input.dispatchEvent(changeEvent);
                            debug('[邀请订单] 设置金额',total_price);

                            await sleepV2(1000);

                            
                        }


                        
                    }
                    //点发送
                    let btn = left_order_area.querySelector('button.el-button.submit-order-btn.el-button--primary.el-button--small');

                    btn.click();

                    for(let i = 0; i < bar_items.length; i++) {
                        let bar_item = bar_items[i];
                        if(bar_item.textContent.includes('最新订单')) {
                            bar_item.click();
                        }
                    }
                }else {
                    debug('[邀请订单]没进入弹窗');
                }
            }else {
                debug('[邀请订单]没找到商品');
            }

        }
   
   
    }

    


    


    console.debug('[debug] 创建订单',orderInfo);
    return true;
}

function parseInputText(input, text) {
    //1. 创建 DataTransfer 对象
    const dataTransfer = new DataTransfer();

    // 2. 设置要粘贴的文本内容
    dataTransfer.setData('text/plain', text);

    // 创建 ClipboardEvent 对象
    const pasteEvent = new ClipboardEvent('paste', {
        clipboardData: dataTransfer,
        bubbles: true,
        cancelable: true
    });
    console.debug('[邀请订单]触发 paste 事件',input,text);
    // 触发 paste 事件
    input.dispatchEvent(pasteEvent);
    input.value = text;
    const inputEvent = new Event('input', { bubbles: true });
    input.dispatchEvent(inputEvent);
    const changeEvent = new Event('change', { bubbles: true });
    input.dispatchEvent(changeEvent);
}