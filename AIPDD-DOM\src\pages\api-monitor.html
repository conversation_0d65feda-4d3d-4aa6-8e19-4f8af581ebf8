<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>PDD API Monitor</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-card h3 {
            margin: 0 0 5px 0;
            color: #666;
            font-size: 14px;
        }
        .stat-card .value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .api-list {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .api-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }
        .api-item:last-child {
            border-bottom: none;
        }
        .api-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .method {
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .method.get { background: #e3f2fd; color: #1976d2; }
        .method.post { background: #e8f5e9; color: #388e3c; }
        .method.put { background: #fff3e0; color: #f57c00; }
        .method.delete { background: #ffebee; color: #d32f2f; }
        .path {
            font-family: monospace;
            font-size: 14px;
            color: #333;
        }
        .count {
            font-size: 12px;
            color: #666;
        }
        .details {
            display: none;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .details.active {
            display: block;
        }
        .details pre {
            margin: 0;
            white-space: pre-wrap;
            font-size: 13px;
        }
        .tabs {
            display: flex;
            margin-bottom: 10px;
        }
        .tab {
            padding: 8px 12px;
            font-size: 13px;
            cursor: pointer;
            border: none;
            background: none;
            color: #666;
        }
        .tab.active {
            color: #1976d2;
            border-bottom: 2px solid #1976d2;
        }
        .copy-btn {
            padding: 4px 8px;
            font-size: 12px;
            background: #f0f0f0;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .copy-btn:hover {
            background: #e0e0e0;
        }
        .search {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PDD API Monitor</h1>
            <div>
                <input type="text" class="search" placeholder="Search APIs...">
                <button onclick="exportAPIs()">Export</button>
            </div>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <h3>Total APIs</h3>
                <div class="value" id="total-apis">0</div>
            </div>
            <div class="stat-card">
                <h3>Total Calls</h3>
                <div class="value" id="total-calls">0</div>
            </div>
            <div class="stat-card">
                <h3>Active APIs (Last 5m)</h3>
                <div class="value" id="active-apis">0</div>
            </div>
            <div class="stat-card">
                <h3>Monitoring Time</h3>
                <div class="value" id="monitor-time">0:00</div>
            </div>
        </div>

        <div class="api-list" id="api-list">
            <!-- APIs will be inserted here -->
        </div>
    </div>
    <script src="../js/api-monitor.js"></script>
</body>
</html>
